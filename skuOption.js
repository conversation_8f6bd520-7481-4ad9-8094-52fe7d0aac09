const obj =
    [
        {
            type: "谐振器",
            en: "",
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "W<PERSON>"}]},
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "9Y", str: "SR9"},
                        {code: "8Y", str: "SR8"},
                        {code: "7Y", str: "SR7"},
                        {code: "5Y", str: "SR5"},
                        {code: "4Y", str: "SR4"},
                        {code: "3Y", str: "SR3"},
                        {code: "2Y", str: "SR2"},
                        {code: "1Y", str: "SR1"},
                        {code: "6W", str: "SR0"},
                        {code: "6Y", str: "SR6"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 9,
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差/带宽",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "±25KHz"},
                        {code: "B", str: "±50KHz"},
                        {code: "C", str: "±75KHz"},
                        {code: "D", str: "±100KHz"},
                        {code: "E", str: "±150KHz"},
                        {code: "F", str: "±250KHz"},
                        {code: "G", str: "3MHz"},
                        {code: "H", str: "±125KHz"},
                    ],
                },
                {
                    name: "插损",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "1.0"},
                        {code: "B", str: "1.2"},
                        {code: "C", str: "1.4"},
                        {code: "D", str: "1.6"},
                        {code: "E", str: "1.8"},
                        {code: "F", str: "1.5"},
                        {code: "G", str: "2.5"},
                        {code: "H", str: "2.0"},
                        {code: "J", str: "2.2"},
                        {code: "K", str: "1.3"},
                        {code: "L", str: "1.7"},
                        {code: "M", str: "6.0"},
                        {code: "N", str: "8.0"},
                    ],
                },
                {
                    name: "工作温度",
                    count: 1,
                    type: "select",
                    list: [
                        // todo 缺少单位，格式不一
                        {code: "A", str: " 0 ~ +50℃"},
                        {code: "B", str: "-10 ~ +60℃"},
                        {code: "C", str: "-20 ~ +70℃"},
                        {code: "D", str: "-40 ~ +85℃"},
                        {code: "E", str: "-30 ~ +85℃"},
                        {code: "F", str: "-40 ~ +95℃"},
                        {code: "G", str: "-40 ~ +105℃"},
                        {code: "H", str: "-40 ~ +125℃"},
                        {code: "I", str: "-55 ~ +125℃"},
                        {code: "J", str: "-30+85℃"},
                        {code: "K", str: "-10~+70"},
                        {code: "L", str: "-45~+85"},
                        {code: "M", str: "-30+80℃"},
                        {code: "N", str: "0~60"},
                        {code: "O", str: "-30~+75"},
                        {code: "P", str: "-20~+75"},
                        {code: "Q", str: "-40~+125"},
                        {code: "S", str: "-20~+85"},
                    ],
                },
                {
                    name: "特殊要求",
                    count: 1,
                    type: "select",
                    list: [
                        // todo 英文
                        {code: "R", str: "RR 要求"},
                        {code: "D", str: "DLD要求"},
                        {code: "S", str: "寄生要求"},
                        {code: "C", str: "C0要求"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "A", str: "纸编带"},
                        {code: "B", str: "青蛙脚"},
                        {code: "E", str: "穿铁衣 "},
                        {code: "F", str: "弯脚"},
                        {code: "G", str: "4脚"},
                        {code: "2", str: "2脚"},
                    ],
                },
            ],
        },
        //todo RTC未配置
        {
            type: "陶振",
            visible: false,
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "WTL"}]},
                {
                    name: "型号",
                    count: 2,
                    type: "select",
                    list: [
                        {code: "9I", str: "ZA1"},
                        {code: "8I", str: "ZA2"},
                        {code: "7I", str: "ZA3"},
                        {code: "6I", str: "ZA4"},
                        {code: "5I", str: "ZA5"},
                        {code: "4I", str: "ZB1"},
                        {code: "3I", str: "ZB2"},
                        {code: "2I", str: "ZB3"},
                        {code: "1I", str: "ZB4"},
                        {code: "9R", str: "ZT1"},
                        {code: "2R", str: "ZT2"},
                        {code: "3R", str: "ZT3"},
                        {code: "4R", str: "ZT4"},
                        {code: "5R", str: "ZT5"},
                        {code: "7R", str: "ZT6"},
                        {code: "8R", str: "ZT7"},
                        {code: "1R", str: "ZT8"},
                        {code: "9I", str: "ZA1"},
                        {code: "8I", str: "ZA2"},
                        {code: "7I", str: "ZA3"},
                        {code: "6I", str: "ZA4"},
                        {code: "1S", str: "CD1"},
                        {code: "2S", str: "CD1"},
                        {code: "6S", str: "ZA6"},
                        {code: "9V", str: "ZB5"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 8,
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "±0.2%"},
                        {code: "B", str: "±0.3%"},
                        {code: "C", str: "±0.5%"},
                        {code: "D", str: "±1%"},
                        {code: "E", str: "±0.25%"},
                        {code: "F", str: "±2KHz"},
                        {code: "G", str: "±2.5KHz"},
                        {code: "H", str: "±3KHz"},
                        {code: "I", str: "±6KHz"},
                        {code: "J", str: "±4KHz"},
                        {code: "K", str: "±0.4%"},
                        {code: "L", str: "±7.5KHz"},
                    ],
                },
                {
                    name: "负载",
                    count: 2,
                    type: "select",
                    list: [
                        {code: "00", str: "无负载"},
                        {code: "10", str: "10pF"},
                        {code: "15", str: "15pF"},
                        {code: "16", str: "16pF"},
                        {code: "22", str: "22pF"},
                        {code: "30", str: "30pF"},
                        {code: "33", str: "33pF"},
                        {code: "39", str: "39pF"},
                        {code: "47", str: "47pF"},
                        {code: "05", str: "5pF"},
                        {code: "A0", str: "100PF"},
                        {code: "A1", str: "120pF/470pF"},
                        {code: "A2", str: "33/39pF"},
                    ],
                },
                {
                    name: "TC偏差量",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "±0.2%"},
                        {code: "B", str: "±0.3%"},
                        {code: "C", str: "±0.5%"},
                        {code: "D", str: "±1%"},
                        {code: "E", str: "±0.25%"},
                        {code: "F", str: "±2KHz"},
                        {code: "G", str: "±2.5KHz"},
                        {code: "H", str: "±3KHz"},
                        {code: "I", str: "±6KHz"},
                        {code: "J", str: "±0.4%"},
                        {code: "N", str: "N/A"},
                        {code: "K", str: "±15KHz"},
                        {code: "2", str: "失真度2%"},
                    ],
                },
                {
                    name: "TC偏差量",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "S", str: "-20+85℃"},
                        {code: "V", str: "-20+80℃"},
                        {code: "W", str: "-25+85℃ "},
                        {code: "X", str: "-40+80℃ "},
                        {code: "C", str: "-20+70℃"},
                        {code: "Q", str: "-40+125℃"},
                        //  todo：这行-没有code 临时处理
                        {code: "-", str: "-20+105℃"},
                        {code: "D", str: "-40+85℃"},
                        {code: "Y", str: "-25+80℃"},
                        {code: "Z", str: "-45+125℃"},
                    ],
                },
                {
                    name: "特殊要求",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "7", str: "RR <70ohms"},
                        {code: "5", str: "RR<50 ohms"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "A", str: "ZTA（2只脚）"},
                        {code: "B", str: "ZTT（3只脚）"},
                        {code: "S", str: "SMD"},
                        {code: "K", str: "加20KHz"},
                    ],
                },
            ],
        },
        {
            type: "音叉",
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "WTL"}]},
                {
                    name: "型号",
                    type: "select",
                    list: [
                        {code: "5X", str: "TS7"},
                        {code: "8W", str: "TS8"},
                        {code: "1W", str: "TS9"},
                        {code: "1X", str: "WX1"},
                        {code: "3X", str: "WX3"},
                        {code: "4X", str: "WX4"},
                        {code: "2T", str: "WA1"},
                        {code: "2X", str: "WX2"},
                        {code: "3T", str: "WT8"},
                        {code: "8A", str: "WA8"},
                        {code: "9A", str: "WA4"},
                        {code: "AW", str: "TS6"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 6,
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    type: "select",
                    list: [
                        {code: "A", str: "±5ppm"},
                        {code: "B", str: "±10ppm"},
                        {code: "C", str: "±15ppm"},
                        {code: "D", str: "±20ppm"},
                        {code: "E", str: "±25ppm"},
                        {code: "F", str: "±30ppm"},
                        {code: "G", str: "±50ppm"},
                        {code: "H", str: "±100ppm"},
                        {code: "I", str: "±150ppm"},
                        {code: "J", str: "±40ppm"},
                        {code: "K", str: "-20~-30ppm"},
                        {code: "L", str: "-50 至 -90ppm"},
                    ],
                },
                {
                    name: "负载",
                    type: "select",
                    list: [
                        {code: "06", str: "6pF"},
                        {code: "07", str: "7pF"},
                        {code: "12", str: "12pF"},
                        {code: "09", str: "9pF"},
                        {code: "20", str: "20pF"},
                        {code: "L5", str: "12.5"},
                        {code: "10", str: "10pF"},
                        {code: "04", str: "4pF"},
                    ],
                },
                {
                    name: "TC偏差量",
                    type: "select",
                    list: [
                        {code: "A", str: "±5ppm"},
                        {code: "B", str: "±10ppm"},
                        {code: "C", str: "±15ppm"},
                        {code: "D", str: "±20ppm"},
                        {code: "E", str: "±25ppm"},
                        {code: "F", str: "±30ppm"},
                        {code: "G", str: "±50ppm"},
                        {code: "H", str: "±100ppm"},
                        {code: "I", str: "±150ppm"},
                        {code: "Z", str: "Total Frequency tolerance"},
                        {code: "J", str: "±40ppm"},
                        {code: "K", str: "0"},
                    ],
                },
                {
                    name: "TC温度",
                    type: "select",
                    list: [
                        // todo 单位
                        {code: "A", str: "  0 ~  +50℃"},
                        {code: "B", str: "-10 ~ +60℃"},
                        {code: "C", str: "-20 ~ +70℃"},
                        {code: "D", str: "-40 ~ +85℃"},
                        {code: "E", str: "-30 ~ +85℃"},
                        {code: "F", str: "-40 ~ +95℃"},
                        {code: "G", str: "-40 ~ +105℃"},
                        {code: "H", str: "-40 ~ +125℃"},
                        {code: "I", str: "-55 ~ +125℃"},
                        {code: "J", str: "-30 ~ +85℃"},
                        {code: "K", str: "-10 ~ +70℃"},
                        {code: "L", str: "-45 ~ +85℃"},
                        {code: "M", str: "-30 ~ +80℃"},
                        {code: "N", str: "0 ~ +60℃"},
                        {code: "O", str: "-30~+75℃"},
                        {code: "P", str: "-20~+75℃"},
                        {code: "Q", str: "-40~+125℃"},
                        {code: "S", str: "-20~+85℃"},
                        {code: "T", str: "-20 ~ +60℃"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    list: [
                        // todo 中文
                        {code: "7", str: "RR <70ohms"},
                        {code: "5", str: "RR<50 ohms"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "L", str: "脚长6mm"},
                        {code: "T", str: "TKD"},
                        {code: "3", str: "RR<30KΩ max"},
                        {code: "A", str: "脚长2.3mm"},
                    ],
                },
            ],
        },
        {
            type: "crystal",
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "WTL"}]},
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "1M", str: "TX1"},
                        {code: "2M", str: "TX2"},
                        {code: "3M", str: "TX3"},
                        {code: "5M", str: "TX5"},
                        {code: "6M", str: "TX6"},
                        {code: "7M", str: "TX7"},
                        {code: "8M", str: "TX8"},
                        {code: "4M", str: "TX9"},
                        {code: "5G", str: "TG5"},
                        {code: "6G", str: "TG6"},
                        {code: "7G", str: "TG7"},
                        {code: "8G", str: "TG8"},
                        {code: "9S", str: "WX6"},
                        {code: "6U", str: "WS6"},
                        {code: "3S", str: "WZ6"},
                        {code: "2A", str: "WA2"},
                        {code: "3A", str: "WA3"},
                        {code: "4N", str: "WZ4"},
                        {code: "4S", str: "WZ5"},
                        {code: "5S", str: "WZ3"},
                        {code: "9M", str: "WX7"},
                        {code: "7S", str: "WZ7"},
                        {code: "9L", str: "WS7"},
                        {code: "9U", str: "WX5"},
                        {code: "8U", str: "WZ1"},
                        {code: "1U", str: "WM1"},
                        {code: "2W", str: "W12"},
                        //  todo:这里没有系列号？
                        {code: "L1", str: "", visible: false},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 8,
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5ppm"},
                        {code: "B", str: "±10ppm"},
                        {code: "C", str: "±15ppm"},
                        {code: "D", str: "±20ppm"},
                        {code: "E", str: "±25ppm"},
                        {code: "F", str: "±30ppm"},
                        {code: "G", str: "±50ppm"},
                        {code: "H", str: "±100ppm"},
                        {code: "I", str: "±150ppm"},
                        {code: "J", str: "±35ppm"},
                        {code: "K", str: "±40ppm "},
                        {code: "L", str: "±7ppm"},
                        {code: "M", str: "±17ppm"},
                        {code: "N", str: "±8ppm"},
                    ],
                },
                {
                    name: "负载",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "06", str: "6pF"},
                        {code: "08", str: "8pF"},
                        {code: "12", str: "12pF"},
                        {code: "I3", str: "9.3pF"},
                        {code: "L5", str: "12.5pF"},
                        {code: "20", str: "20pf"},
                        {code: "00", str: "无负载"},
                        {code: "18", str: "18pF"},
                        {code: "09", str: "9PF"},
                        {code: "16", str: "16PF"},
                        {code: "30", str: "30pf"},
                        {code: "33", str: "33ppF"},
                        {code: "15", str: "15PF"},
                        {code: "10", str: "10PF"},
                        {code: "13", str: "13PF"},
                        {code: "I5", str: "9.5pF"},
                        {code: "I8", str: "9.8pF"},
                        {code: "07", str: "7pF"},
                    ],
                },
                {
                    name: "TC偏差量",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5ppm"},
                        {code: "B", str: "±10ppm"},
                        {code: "C", str: "±15ppm"},
                        {code: "D", str: "±20ppm"},
                        {code: "E", str: "±25ppm"},
                        {code: "F", str: "±30ppm"},
                        {code: "G", str: "±50ppm"},
                        {code: "H", str: "±100ppm"},
                        {code: "I", str: "±150ppm"},
                        {code: "J", str: "±35ppm"},
                        {code: "K", str: "±6ppm"},
                        {code: "L", str: "±40ppm"},
                    ],
                },
                {
                    name: "TC温度",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 格式不一，单位
                        {code: "A", str: "  0 ~  +50℃"},
                        {code: "B", str: "-10 ~ +60℃"},
                        {code: "C", str: "-20 ~ +70℃"},
                        {code: "D", str: "-40 ~ +85℃"},
                        {code: "E", str: "-30 ~ +85℃"},
                        {code: "F", str: "-40 ~ +95℃"},
                        {code: "G", str: "-40 ~ +105℃"},
                        {code: "H", str: "-40 ~ +125℃"},
                        {code: "I", str: "-55 ~ +125℃"},
                        {code: "J", str: "-30 ~ +70℃"},
                        {code: "K", str: "-10 ~ +70℃"},
                        {code: "L", str: "-45 ~ +85℃"},
                        {code: "M", str: "-30 ~ +80℃"},
                        {code: "N", str: "0 ~ +60℃"},
                        {code: "O", str: "-30~+75℃"},
                        {code: "P", str: "-20~+75℃"},
                        {code: "Q", str: "-40~+125℃"},
                        {code: "R", str: "0~70℃"},
                        {code: "S", str: "-20~+85℃"},
                        {code: "T", str: "-20+60℃"},
                        {code: "U", str: "-20+105℃"},
                        {code: "V", str: "-20+80℃"},
                        {code: "W", str: "-25+85℃ "},
                        {code: "X", str: "-40+80℃ "},
                        {code: "Y", str: "-25+80℃"},
                        {code: "Z", str: "-45+125℃"},
                        {code: "1", str: "10°C~50℃"},
                        {code: "2", str: "-10~+85℃"},
                        {code: "3", str: "-10~+75℃"},
                        {code: "4", str: "-40 to +90℃"},
                        {code: "5", str: "-30 ~ +105℃"},
                    ],
                },
                {
                    name: "振动模式",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "1", str: "AT Fundamental"},
                        {code: "3", str: "AT 3RD"},
                        {code: "5", str: "AT 5TH"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "R", str: "RR 要求"},
                        {code: "D", str: "DLD要求"},
                        {code: "S", str: "寄生要求"},
                        {code: "C", str: "C0要求"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "A", str: "纸编带"},
                        {code: "E", str: "剪脚"},
                        {code: "F", str: "穿铁衣 "},
                        {code: "B", str: "弯脚"},
                        {code: "G", str: "4脚"},
                        {code: "H", str: "3脚"},
                        {code: "K", str: "ESR 20 ohm max"},
                        {code: "2", str: "ESR 25 ohm max"},
                        {code: "5", str: "ESR 50 ohm max"},
                        {code: "3", str: "ESR 30 ohm max"},
                        {code: "6", str: "ESR 60 ohm max"},
                        {code: "R", str: "ESR 100 ohm max"},
                        {code: "4", str: "ESR 40 ohm max"},
                        {code: "Z", str: "200 Ohm"},
                        {code: "8", str: "ESR 80 ohm max"},
                        {code: "7", str: "ESR 70 ohm max"},
                        {code: "L", str: "ESR 120 ohm max"},
                        {code: "9", str: "ESR 45 ohm max"},
                        {code: "I", str: "ESR 35 ohm max"},
                    ],
                },
            ],
        },
        {
            type: "钟振",
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "WTL"}]},
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "1K", str: "TC1"},
                        {code: "2K", str: "TC2"},
                        {code: "3K", str: "TC3"},
                        {code: "5K", str: "TC5"},
                        {code: "7K", str: "TC7"},
                        {code: "4K", str: "TK2"},
                        {code: "6K", str: "TK3"},
                        {code: "8K", str: "TK4"},
                        {code: "9K", str: "TK4"},
                        {code: "1D", str: "TU2"},
                        {code: "2D", str: "TU3"},
                        {code: "3D", str: "TW2"},
                        {code: "4D", str: "TW3"},
                        {code: "5D", str: "TN2"},
                        {code: "6D", str: "TN3"},
                        {code: "7D", str: "TN7"},
                        {code: "8F", str: "LO1"},
                        {code: "8H", str: "LO2"},
                        {code: "1B", str: "TB1"},
                        {code: "2B", str: "TB2"},
                        {code: "3B", str: "TB3"},
                        {code: "5B", str: "TB4"},
                        {code: "7B", str: "TB5"},
                        {code: "1P", str: "TL2"},
                        {code: "2P", str: "TL3"},
                        {code: "3P", str: "TL4"},
                        {code: "4P", str: "TL5"},
                        {code: "5P", str: "TL6"},
                        {code: "6P", str: "TL7"},
                        {code: "1Q", str: "TP3"},
                        {code: "2Q", str: "TP5"},
                        {code: "3Q", str: "TP7"},
                        {code: "4Q", str: "TS7"},
                        {code: "1T", str: "VC9"},
                        {code: "AC", str: "VC4"},
                        {code: "1C", str: "VC1"},
                        {code: "2C", str: "VC2"},
                        {code: "3C", str: "VC3"},
                        {code: "5C", str: "VC5"},
                        {code: "7C", str: "VC7"},
                        {code: "8C", str: "VC8"},
                        {code: "4C", str: "VP3"},
                        {code: "9C", str: "VW5"},
                        {code: "6C", str: "VA7"},
                        {code: "9T", str: "VS7"},
                        {code: "7T", str: "VT7"},
                        {code: "8T", str: "VF2"},
                        {code: "6T", str: "WT1"},
                        {code: "1H", str: "OC9"},
                        {code: "2H", str: "OC8"},
                        {code: "3H", str: "OCH"},
                        {code: "4H", str: "OCW"},
                        {code: "5H", str: "OSD"},
                        {code: "6H", str: "OLH"},
                        {code: "7H", str: "OCN"},
                        {code: "8B", str: "OSH"},
                        {code: "9H", str: "OCD"},
                        {code: "2V", str: "PV3"},
                        {code: "4V", str: "PV5"},
                        {code: "6V", str: "PV7"},
                        {code: "3V", str: "PL3"},
                        {code: "5V", str: "PL5"},
                        {code: "7V", str: "PL7"},
                        {code: "5L", str: "CL5"},
                        {code: "7L", str: "CL7"},
                        {code: "8L", str: "CV5"},
                        {code: "6L", str: "CV7"},
                        {code: "8V", str: "CV8"},
                        {code: "1L", str: "LV1"},
                        {code: "2L", str: "LV2"},
                        {code: "1G", str: "TC8"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 8,
                    generate: frequencyGenerate,
                },

                {
                    name: "总频差",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5ppm"},
                        {code: "B", str: "±10ppm"},
                        {code: "C", str: "±15ppm"},
                        {code: "D", str: "±20ppm"},
                        {code: "E", str: "±25ppm"},
                        {code: "F", str: "±30ppm"},
                        {code: "G", str: "±50ppm"},
                        {code: "H", str: "±100ppm"},
                        {code: "I", str: "±0.5ppm"},
                        {code: "J", str: "±1.0ppm"},
                        {code: "K", str: "±1.5ppm"},
                        {code: "T", str: "±2.0ppm"},
                        {code: "P", str: "±2.5ppm"},
                        {code: "Q", str: "±0.05ppm"},
                        {code: "R", str: "±0.1ppm"},
                        {code: "S", str: "±0.2ppm"},
                        // todo 正负？0？
                        {code: "T", str: "0"},
                        {code: "U", str: "95.8464ppm"},
                        {code: "V", str: "±4.6ppm"},
                        {code: "W", str: "±5.0ppm"},
                        {code: "X", str: "±20PPb"},
                    ],
                },
                {
                    name: "供电电压",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "18", str: "1.8V"},
                        {code: "25", str: "2.5V"},
                        {code: "28", str: "2.8V "},
                        {code: "33", str: "3.3V"},
                        {code: "50", str: "5.0V"},
                        {code: "12", str: "1.2V"},
                        {code: "90", str: "9.0V"},
                        {code: "09", str: "0.9V"},
                        {code: "30", str: "3.0V"},
                        {code: "99", str: "1.8~3.3V"},
                        {code: "88", str: "-0.3~4.6V"},
                        {code: "77", str: "1.7~3.3V"},
                        {code: "66", str: "2.8~3.3V"},
                        {code: "55", str: "1.7V~3.465V"},
                        {code: "A", str: "12V"},
                        {code: "44", str: "1.6~3.63V"},
                        {code: "22", str: "2.2v"},
                    ],
                },
                {
                    name: "TC偏差量",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5ppm"},
                        {code: "B", str: "±10ppm"},
                        {code: "C", str: "±15ppm"},
                        {code: "D", str: "±20ppm"},
                        {code: "E", str: "±25ppm"},
                        {code: "F", str: "±30ppm"},
                        {code: "G", str: "±50ppm"},
                        {code: "H", str: "±100ppm"},
                        {code: "I", str: "±150ppm"},
                        {code: "J", str: "±40ppm"},
                        //  todo 中文？
                        {code: "Z", str: "总频差"},
                        {code: "K", str: "±0.5ppm"},
                        {code: "L", str: "±3ppb"},
                        {code: "M", str: "±0.28"},
                        {code: "N", str: "±2.5ppm"},
                        //  todo O没有对应ppm
                        {code: "O", str: ""},
                        {code: "P", str: "±1.0ppm"},
                        {code: "Q", str: "±2ppm"},
                        {code: "R", str: "±3ppm"},
                        {code: "S ", str: "±50ppb"},
                        {code: "T", str: "±0.1ppm"},
                        {code: "U", str: "±0.14ppm"},
                        {code: "V", str: "±0.2ppm"},
                        {code: "W", str: "±0.28ppm"},
                        {code: "X", str: "±0.37ppm"},
                        {code: "Y", str: "±0.5ppm"},
                        {code: "Z", str: "±0.5ppb"},
                        {code: "02", str: "±0.2ppb"},
                        {code: "05", str: "±0.5ppb"},
                        {code: "1", str: "±1ppb"},
                        {code: "5", str: "±5ppb"},
                        {code: "10", str: "±10ppb"},
                        {code: "20", str: "±20ppb"},
                        {code: "30", str: "±30ppb"},
                    ],
                },
                {
                    name: "TC温度",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 格式不一，单位
                        {code: "A", str: "  0 ~  +50℃"},
                        {code: "B", str: "-10 ~ +60℃"},
                        {code: "C", str: "-20 ~ +70℃"},
                        {code: "D", str: "-40 ~ +85℃"},
                        {code: "E", str: "-30 ~ +85℃"},
                        {code: "F", str: "-40 ~ +95℃"},
                        {code: "G", str: "-40 ~ +105℃"},
                        {code: "H", str: "-40 ~ +125℃"},
                        {code: "I", str: "-55 ~ +125℃"},
                        {code: "J", str: "-30 ~ +85℃"},
                        {code: "K", str: "-10 ~ +70℃"},
                        {code: "L", str: "-45 ~ +85℃"},
                        {code: "M", str: "-30 ~ +80℃"},
                        {code: "N", str: "0 ~ +60℃"},
                        {code: "O", str: "-30~+75℃"},
                        {code: "P", str: "-20~+75℃"},
                        {code: "Q", str: "-40~+125℃"},
                        {code: "R", str: "0~70℃"},
                        {code: "S", str: "-20~+85℃"},
                        {code: "T", str: "-20+60℃"},
                        {code: "U", str: "-20+105℃"},
                        {code: "V", str: "-20+80℃"},
                        {code: "W", str: "-25+85℃"},
                        {code: "X", str: "-40+80℃"},
                        {code: "Y", str: "-25+80℃"},
                        {code: "Z", str: "-45+125℃"},
                        {code: "7", str: " -30+70℃"},
                        {code: "5", str: "-25-70℃"},
                        {code: "4", str: "-40 +70℃"},
                        {code: "8", str: "-50 +85℃"},
                    ],
                },
                {
                    name: "输出模式",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "C", str: " CMOS/HCMOS（方波）"},
                        {code: "P", str: " PECL(6脚）"},
                        {code: "L", str: " LVDS"},
                        {code: "T", str: " TTL"},
                        {code: "H", str: "HCSL"},
                        {code: "M", str: "CML"},
                        {code: "V", str: " LVPECL"},
                        {code: "A", str: "Clipped Sine Wave（削峰正弦波TCXO）"},
                        {code: "B", str: "Sine Wave（正弦波OC）"},
                        {code: "E", str: "Pulse"},
                        {code: "D", str: "LVTTL"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    count: 1,
                    // todo 中文
                    list: [
                        {code: "L", str: " Low Jitter要求"},
                        {code: "P", str: "Phase Noise 要求"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "Q", str: "电流：10mA max"},
                    ],
                },
            ],
        },
        {
            type: "天线",
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "WTL"}]},
                {
                    name: "型号",
                    type: "select",
                    list: [{code: "DA", str: "DA"}],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 9,
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    type: "select",
                    list: [
                        {code: "A", str: "±5"},
                        {code: "B", str: "±10"},
                        {code: "C", str: "±1.0"},
                        {code: "D", str: "±1.5"},
                        {code: "E", str: "±2.0"},
                        {code: "F", str: "±2.5"},
                        {code: "G", str: "±3.0"},
                        {code: "H", str: "±4.0"},
                        {code: "J", str: "±4.5"},
                        {code: "K", str: "±1.023"},
                    ],
                },
                {
                    name: "极化方式",
                    type: "select",
                    list: [
                        {code: "R", str: "RHCP"},
                        {code: "L", str: "LHCP"},
                        {code: "P", str: "Linear Polarization"},
                    ],
                },
                {
                    name: "长X宽",
                    type: "select",
                    list: [
                        {code: "3535", str: "3535"},
                        {code: "2525", str: "2525"},
                        {code: "2020", str: "2020"},
                        {code: "1818", str: "1818"},
                        {code: "2006", str: "2006"},
                        {code: "1606", str: "1606"},
                    ],
                },
                {
                    name: "应用",
                    type: "select",
                    list: [
                        {code: "G", str: "GPS"},
                        {code: "B", str: "BD"},
                        {code: "S", str: "Glonass"},
                        {code: "A", str: "GPS+BD"},
                        {code: "C", str: "GPS+Glonass"},
                        {code: "D", str: "DSRC"},
                        {code: "E", str: "ETC"},
                        {code: "R", str: "RFID"},
                    ],
                },
            ],
        },
        {
            type: "Filter滤波器",
            param: [
                {name: "品牌", type: "select", list: [{code: "W", str: "WTL"}]},
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "1Z", str: "CF1"},
                        {code: "2Z", str: "CF1"},
                        {code: "3Z", str: "CF2"},
                        {code: "4Z", str: "CF2"},
                        {code: "5Z", str: "CF3"},
                        {code: "6Z", str: "CF3"},
                        {code: "7Z", str: "CF4"},
                        {code: "8Z", str: "CF5"},
                        {code: "9Z", str: "CF5"},
                        {code: "AZ", str: "CF6"},
                        {code: "9E", str: "SF9"},
                        {code: "8E", str: "SF8"},
                        {code: "7E", str: "SF7"},
                        {code: "5E", str: "SF5"},
                        {code: "4E", str: "SF4"},
                        {code: "3E", str: "SF3"},
                        {code: "2E", str: "SF2"},
                        {code: "1E", str: "SF1"},
                        {code: "6E", str: "SF0"},
                        {code: "1J", str: "SW1"},
                        {code: "2J", str: "SW2"},
                        {code: "3J", str: "SW3"},
                        {code: "4J", str: "SW4"},
                        {code: "5J", str: "SW5"},
                        {code: "6J", str: "SW6"},
                        {code: "7J", str: "SW7"},
                        {code: "8J", str: "SW8"},
                        {code: "9J", str: "SW9"},
                        {code: "1F", str: "WF1"},
                        {code: "2F", str: "WF2"},
                        {code: "3F", str: "WF3"},
                        {code: "4F", str: "WF4"},
                        {code: "5F", str: "WF5"},
                        {code: "6F", str: "WF6"},
                        {code: "7F", str: "WF7"},
                        {code: "1A", str: "SWA"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    length: 9,
                    generate: frequencyGenerate,
                },
                {
                    name: "类别",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "C", str: "ceramic fiter"},
                        {code: "S", str: " Saw Filter"},
                        {code: "Q", str: " Crystal Filter"},
                    ],
                },
                {
                    name: "带宽",
                    type: "select",
                    list: [
                        {code: "51", str: "51MHz"},
                        {code: "05", str: "5MHz"},
                        //  todo 中文
                        {code: "TW", str: "多种带宽"},
                        {code: "06", str: "6KHz "},
                        {code: "15", str: "7.5KHz "},
                        {code: "02", str: "2MHz"},
                        {code: "0A", str: "1.75MHz"},
                        {code: "00", str: "无带宽"},
                        {code: "0B", str: "1.2MHz"},
                        {code: "08", str: " 8MHz"},
                        {code: "0C", str: "31.24"},
                        {code: "12", str: "12MHz"},
                        {code: "01", str: " 1MHz"},
                        {code: "20", str: "20MHz"},
                        {code: "A3", str: "1.3MHz"},
                        {code: "45", str: "45MHz"},
                        {code: "0D", str: "3.75KHz"},
                        {code: "17", str: "17MHZ"},
                        {code: "0E", str: "18.6KHz"},
                        {code: "02", str: "2KHz"},
                        {code: "40", str: "40MHz"},
                        {code: "0F", str: "1.71MHz"},
                        {code: "A5", str: "1.5KHz"},
                        {code: "A1", str: "11KHz"},
                        {code: "B5", str: "2.5KHz"},
                        {code: "04", str: "4KHz"},
                        {code: "N5", str: "0.5KHz"},
                        {code: "A2", str: "1.2KHz"},
                        {code: "0G", str: "0.15KHz"},
                        {code: "0H", str: "0.12KHz"},
                        {code: "0I", str: "2.2KHz"},
                        {code: "0J", str: "0.08KHz"},
                        {code: "0K", str: "0.95KHz"},
                        {code: "0L", str: "0.4KHz"},
                        {code: "0M", str: "1.1KHz"},
                        {code: "0N", str: "0.55KHz"},
                        {code: "0O", str: "15KHz"},
                        {code: "0P", str: "34KHz"},
                        {code: "0Q", str: "2.778KHz"},
                        {code: "0R", str: "19KHz"},
                        {code: "0S", str: "7.0KHz"},
                        {code: "0T", str: "60KHz"},
                        {code: "0U", str: "5.0KHz"},
                        {code: "0V", str: "12KHz"},
                        {code: "0W", str: "17KHz"},
                        {code: "0X", str: "5.5KHz"},
                        {code: "0Y", str: "1.7KHz"},
                        {code: "0Z", str: "3.5KHz"},
                        {code: "1A", str: "3.8KHz"},
                        {code: "1B", str: "9KHz"},
                        {code: "1C", str: "26KHz"},
                        {code: "1D", str: "40kHz"},
                        {code: "1E", str: "80kHz"},
                        {code: "1F", str: "3.94MHz"},
                        {code: "31", str: "31MHz"},
                        {code: "1G", str: "46.41MHz"},
                    ],
                },
                {
                    name: "插损",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "01", str: " 1.0 dB"},
                        {code: "22", str: "2.2dB"},
                        {code: "05", str: "5.0dB"},
                        {code: "02", str: "2.0dB"},
                        {code: "03", str: "3.0dB"},
                        {code: "04", str: "4.0dB"},
                        {code: "0A", str: " 1.4dB"},
                        {code: "0B", str: "2.5dB"},
                        {code: "1B", str: "4.1dB"},
                        {code: "35", str: "3.5dB"},
                        {code: "18", str: "1.8dB"},
                        {code: "0B", str: "1.3dB"},
                        {code: "15", str: "1.5dB"},
                        {code: "06", str: "6.0dB"},
                        {code: "21", str: "21dB"},
                        {code: "20", str: "20dB"},
                        {code: "16", str: "1.6dB"},
                        {code: "45", str: " 4.5dB"},
                        {code: "28", str: " 2.8dB"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "N", str: "无"},
                        {code: "D", str: "双模"},
                        {code: "T", str: "三模"},
                        {code: "3", str: "3RD"},
                    ],
                },
            ],
        },
    ];

const seriesTypeAndSizeMap = [
    {
        "type": "Tuning Fork SMD",
        "s": "TS7",
        "size": "1.2*1.0/2/SMD",
        "code": "5X"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "TS8",
        "size": "1.6*1.0/2/SMD",
        "code": "8W"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "TS9",
        "size": "2.0*1.2/2/SMD",
        "code": "1W"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "TS6",
        "size": "2.0*1.2/4/SMD",
        "code": "AW"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "WX1",
        "size": "3.2*1.5/2/SMD",
        "code": "1X"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "WX3",
        "size": "3.8*8/4/SMD",
        "code": "3X"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "WX4",
        "size": "6.9*1.4/4/SMD",
        "code": "4X"
    },
    {
        "type": "Tuning Fork Dip",
        "s": "WA1",
        "size": "1.2*4.5/DIP",
        "code": "2T"
    },
    {
        "type": "Tuning Fork DIP",
        "s": "WX2",
        "size": "2*6/DIP",
        "code": "2X"
    },
    {
        "type": "Tuning Fork DIP",
        "s": "WT8",
        "size": "3*8/DIP",
        "code": "3T"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "WA8",
        "size": "2*6/青蛙脚SMD",
        "code": "8A"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "WA4",
        "size": "2*6/青蛙脚SMD+铁衣",
        "code": "9A"
    },
    {
        "type": "Tuning Fork SMD",
        "s": "WA5",
        "size": "7.5*3.1/SMD",
        "code": "5A"
    },
    {
        "type": "Crystal",
        "s": "TX1",
        "size": "2.0*1.6/4/SMD",
        "code": "1M"
    },
    {
        "type": "Crystal",
        "s": "TX2",
        "size": "2.5*2.0/4/SMD",
        "code": "2M"
    },
    {
        "type": "Crystal",
        "s": "TX3",
        "size": "3.2*2.5/4/SMD",
        "code": "3M"
    },
    {
        "type": "Crystal",
        "s": "TX5",
        "size": "5.0*3.2/4/SMD",
        "code": "5M"
    },
    {
        "type": "Crystal",
        "s": "TX4",
        "size": "5.0*3.2/2/SMD",
        "code": "4M"
    },
    {
        "type": "Crystal",
        "s": "TB6",
        "size": "6.0*3.5/2/SMD",
        "code": "6B"
    },
    {
        "type": "Crystal",
        "s": "TX6",
        "size": "6.0*3.5/4/SMD",
        "code": "6M"
    },
    {
        "type": "Crystal",
        "s": "TX7",
        "size": "7.0*5.0/4/SMD",
        "code": "7M"
    },
    {
        "type": "Crystal",
        "s": "TX8",
        "size": "1.6*1.2/4/SMD",
        "code": "8M"
    },
    {
        "type": "Crystal",
        "s": "TX9",
        "size": "1.2*1.0/4/SMD",
        "code": "0M"
    },
    {
        "type": "Crystal",
        "s": "TG5",
        "size": "5.0*3.2/2/Glass",
        "code": "5G"
    },
    {
        "type": "Crystal",
        "s": "TG6",
        "size": "6.0*3.5/2/Glass",
        "code": "6G"
    },
    {
        "type": "Crystal",
        "s": "TG7",
        "size": "7.0*5.0/2/Glass",
        "code": "7G"
    },
    {
        "type": "Crystal",
        "s": "TG8",
        "size": "8.0*4.5/2/Glass",
        "code": "8G"
    },
    {
        "type": "Crystal",
        "s": "WX6",
        "size": "49S",
        "code": "9S"
    },
    {
        "type": "Crystal",
        "s": "WS6",
        "size": "49S矮外壳",
        "code": "6U"
    },
    {
        "type": "Crystal",
        "s": "WZ6",
        "size": "49S/3脚",
        "code": "3S"
    },
    {
        "type": "Crystal",
        "s": "WA2",
        "size": "2X6",
        "code": "2A"
    },
    {
        "type": "Crystal",
        "s": "WA3",
        "size": "3X8",
        "code": "3A"
    },
    {
        "type": "Crystal",
        "s": "WZ4",
        "size": "49SMD/4脚",
        "code": "4N"
    },
    {
        "type": "Crystal",
        "s": "WZ5",
        "size": "49SMD 4PAD",
        "code": "4S"
    },
    {
        "type": "Crystal",
        "s": "WZ3",
        "size": "49SMD/3脚",
        "code": "5S"
    },
    {
        "type": "Crystal",
        "s": "WX7",
        "size": "49SMD",
        "code": "9M"
    },
    {
        "type": "Crystal",
        "s": "WZ7",
        "size": "49SMD矮外壳",
        "code": "7S"
    },
    {
        "type": "Crystal",
        "s": "WM7",
        "size": "Mini-49SMD（小）",
        "code": "9L"
    },
    {
        "type": "Crystal",
        "s": "WX5",
        "size": "49U/49T",
        "code": "9U"
    },
    {
        "type": "Crystal",
        "s": "WZ1",
        "size": "51U",
        "code": "8U"
    },
    {
        "type": "Crystal",
        "s": "WM1",
        "size": "UM-1/5/SMD",
        "code": "1U"
    },
    {
        "type": "Crystal",
        "s": "W12",
        "size": "12.4*4.8/4/SMD",
        "code": "2W"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZA1",
        "size": "ZTA/ZTT/10*10*5/DIP",
        "code": "9I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZA2",
        "size": "ZTA/ZTT/10*7.5*4/DIP",
        "code": "8I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZA3",
        "size": "ZTA/ZTT/9.5*5.5*4/DIP",
        "code": "7I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZA4",
        "size": "ZTA/ZTT/5.5*5.5*3/DIP",
        "code": "6I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZA5",
        "size": "ZTA/ZTT/5.5*6.0*3/DIP",
        "code": "5I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZA6",
        "size": "ZTA/ZTT/10*8*3.5/DIP",
        "code": "6S"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZB1",
        "size": "ZTB/6.0*5.0*3.5/DIP",
        "code": "4I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZB2",
        "size": "ZTB/9.0*7.0*3.0/DIP",
        "code": "3I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZB3",
        "size": "ZTB/6.0*5.0*2.3/弯脚",
        "code": "2I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZB4",
        "size": "ZTB/8.5*7.5*5/弯脚",
        "code": "1I"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZB5",
        "size": "ZTB/9.0*7.0*3.5/SMD",
        "code": "9V"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT1",
        "size": "ZTT/7.4*3.4*1.8/SMD",
        "code": "9R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT2",
        "size": "ZTT/2.5*2.0*1.2/SMD",
        "code": "2R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT3",
        "size": "ZTT/3.2*1.3/3/SMD",
        "code": "3R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT4",
        "size": "ZTT/3.7*3.1/3/SMD",
        "code": "4R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT5",
        "size": "ZTT/4.5*2.0/3/SMD",
        "code": "5R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT6",
        "size": "ZTT/4.7*4.1/ZTT/SMD",
        "code": "7R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT7",
        "size": "ZTT/6.0*3.0/3/SMD",
        "code": "8R"
    },
    {
        "type": "Ceramic Resonator",
        "s": "ZT8",
        "size": "ZTT/2.2*1.8*1.2/SMD",
        "code": "1R"
    },
    {
        "type": "陶瓷鉴频器",
        "s": "CD1",
        "size": "6.0*3.0*6.3/2脚DIP/陶瓷鉴频器   (含SMD）",
        "code": "1S"
    },
    {
        "type": "陶瓷鉴频器",
        "s": "CD2",
        "size": "9.0*7.0*4.0/2脚DIP/陶瓷鉴频器   (含SMD）",
        "code": "2S"
    },
    {
        "type": "SAW Resonator",
        "s": "SR9",
        "size": "TO39/9.15*3.15",
        "code": "9Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR8",
        "size": "F11/11.0*4.5*3.2",
        "code": "8Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR7",
        "size": "D11/8.36*3.45",
        "code": "7Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR5",
        "size": "5.0*5.0",
        "code": "5Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR6",
        "size": "5.0*3.5",
        "code": "6Y"
    },
    {
        "type": "",
        "s": "SR0",
        "size": "7.4*3.4*1.8mm/3/SMD",
        "code": "6W"
    },
    {
        "type": "SAW Resonator",
        "s": "SR4",
        "size": "3.8*3.8",
        "code": "4Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR3",
        "size": "3.0*3.0",
        "code": "3Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR1",
        "size": "3.2*2.5",
        "code": "2Y"
    },
    {
        "type": "SAW Resonator",
        "s": "SR2",
        "size": "2.0*1.6",
        "code": "1Y"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF1",
        "size": "陶瓷滤波器/8*7*8/3/DIP",
        "code": "1Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF1",
        "size": "陶瓷滤波器/11*8*7/5/DIP",
        "code": "2Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF2",
        "size": "陶瓷滤波器/9.5*6.5*6.3/5/DIP",
        "code": "3Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF2",
        "size": "陶瓷滤波器/6.5*6.5*6.3/4/DIP",
        "code": "4Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF3",
        "size": "陶瓷滤波器/7.5*6*4/3 SMD",
        "code": "5Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF3",
        "size": "陶瓷滤波器/11.9*7.5*3/4/SMD",
        "code": "6Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF4",
        "size": "陶瓷滤波器/'7.0*7.0*4.0/3/DIP",
        "code": "7Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF5",
        "size": "陶瓷滤波器/7.0*3.0*1.5/3/SMD",
        "code": "8Z"
    },
    {
        "type": "陶瓷滤波器",
        "s": "CF5",
        "size": "陶瓷滤波器/3.45*3.1*1.4/4/SMD",
        "code": "9Z"
    },
    {
        "type": "陶瓷鉴频器",
        "s": "CD1",
        "size": "\"DIP 6.0 x 6.3 x 3.0",
        "code": ""
    },
    {
        "type": "SMD 6.0 x 6.2 x 3.1\"",
        "s": "1S",
        "size": "",
        "code": ""
    },
    {
        "type": "陶瓷鉴频器",
        "s": "CD2",
        "size": "\"DIP 9.0 x 7.0 x 4.0",
        "code": ""
    },
    {
        "type": "SMD 3.7 x 3.1 x 1.4\"",
        "s": "2S",
        "size": "",
        "code": ""
    },
    {
        "type": "Saw Filter",
        "s": "SF9",
        "size": "TO39/9.15*3.15",
        "code": "9E"
    },
    {
        "type": "Saw Filter",
        "s": "SF8",
        "size": "F11/11.0*4.5*3.2",
        "code": "8E"
    },
    {
        "type": "Saw Filter",
        "s": "SF5",
        "size": "5.0*5.0",
        "code": "5E"
    },
    {
        "type": "Saw Filter",
        "s": "SF4",
        "size": "3.8*3.8",
        "code": "4E"
    },
    {
        "type": "Saw Filter",
        "s": "SF3",
        "size": "3.0*3.0",
        "code": "3E"
    },
    {
        "type": "Saw Filter",
        "s": "SF2",
        "size": "2.0*1.5",
        "code": "2E"
    },
    {
        "type": "Saw Filter",
        "s": "SF1",
        "size": "1.4*1.1",
        "code": "1E"
    },
    {
        "type": "Saw Filter",
        "s": "SF0",
        "size": "1.1*0.9",
        "code": "6E"
    },
    {
        "type": "Saw Filter",
        "s": "SW1",
        "size": "36x16",
        "code": "1J"
    },
    {
        "type": "Saw Filter",
        "s": "SW2",
        "size": "34.8x12.6",
        "code": "2J"
    },
    {
        "type": "Saw Filter",
        "s": "SW3",
        "size": "27.2X12",
        "code": "3J"
    },
    {
        "type": "Saw Filter",
        "s": "SW4",
        "size": "22.5x17.5",
        "code": "4J"
    },
    {
        "type": "Saw Filter",
        "s": "SW5",
        "size": "22.1x12.64",
        "code": "5J"
    },
    {
        "type": "Saw Filter",
        "s": "SW6",
        "size": "19.50x6.50",
        "code": "6J"
    },
    {
        "type": "Saw Filter",
        "s": "SW7",
        "size": "14.5x10.0",
        "code": "7J"
    },
    {
        "type": "Saw Filter",
        "s": "SW8",
        "size": "13.30x6.50",
        "code": "8J"
    },
    {
        "type": "Saw Filter",
        "s": "SW9",
        "size": "12.0*7.2",
        "code": "9J"
    },
    {
        "type": "OSC",
        "s": "TC1",
        "size": "2.0*1.6/4/OSC",
        "code": "1K"
    },
    {
        "type": "OSC",
        "s": "TC2",
        "size": "2.5*2.0/4/OSC",
        "code": "2K"
    },
    {
        "type": "OSC",
        "s": "TC3",
        "size": "3.2*2.5/4/OSC",
        "code": "3K"
    },
    {
        "type": "OSC",
        "s": "TC5",
        "size": "5.0*3.2/4/OSC",
        "code": "5K"
    },
    {
        "type": "OSC",
        "s": "TC7",
        "size": "7.0*5.0/4/OSC",
        "code": "7K"
    },
    {
        "type": "OSC",
        "s": "TK2",
        "size": "2.5*2.0/4/OSC/32.768KHz",
        "code": "4K"
    },
    {
        "type": "OSC",
        "s": "TK3",
        "size": "3.2*2.5/4/OSC/32.768KHz",
        "code": "6K"
    },
    {
        "type": "OSC",
        "s": "TK4",
        "size": "2.5*2.0/4/OSC/32.768KHz/Low Current Consumption",
        "code": "8K"
    },
    {
        "type": "OSC",
        "s": "TU2",
        "size": "2.5*2.0/4/OSC/Low power",
        "code": "1D"
    },
    {
        "type": "OSC",
        "s": "TU3",
        "size": "3.2*2.5/4/OSC/Low power",
        "code": "2D"
    },
    {
        "type": "OSC",
        "s": "TW2",
        "size": "2.5*2.0/4/OSC/Extended operating temperature",
        "code": "3D"
    },
    {
        "type": "OSC",
        "s": "TW3",
        "size": "3.2*2.5/4/OSC/Extended operating temperature",
        "code": "4D"
    },
    {
        "type": "OSC",
        "s": "TN2",
        "size": "2.5*2.0/4/OSC/ultra low noise",
        "code": "5D"
    },
    {
        "type": "OSC",
        "s": "TN3",
        "size": "3.2*2.5/4/OSC/ultra low noise",
        "code": "6D"
    },
    {
        "type": "OSC",
        "s": "TN7",
        "size": "7.0*5.0/4/OSC/ultra low noise",
        "code": "7D"
    },
    {
        "type": "OSC",
        "s": "LO1",
        "size": "14Pin Full Size",
        "code": "8F"
    },
    {
        "type": "OSC",
        "s": "LO2",
        "size": "8Pin Half Size",
        "code": "8H"
    },
    {
        "type": "OSC",
        "s": "TB1",
        "size": "2.0*1.6/4/OSC/Fast deliver",
        "code": "1B"
    },
    {
        "type": "OSC",
        "s": "TB2",
        "size": "2.5*2.0/4/OSC/Fast deliver",
        "code": "2B"
    },
    {
        "type": "OSC",
        "s": "TB3",
        "size": "3.2*2.5/4/OSC/Fast deliver",
        "code": "3B"
    },
    {
        "type": "OSC",
        "s": "TB4",
        "size": "5.0*3.2/4/OSC/Fast deliver",
        "code": "5B"
    },
    {
        "type": "OSC",
        "s": "TB5",
        "size": "7.0*5.0/4/OSC/Fast deliver",
        "code": "7B"
    },
    {
        "type": "OSC Differential",
        "s": "TL2",
        "size": "3.2*2.5/6/CMOS/Fast deliver",
        "code": "1P"
    },
    {
        "type": "OSC Differential",
        "s": "TL3",
        "size": "3.2*2.5/6/LVPECL, LVDS/Fast deliver",
        "code": "2P"
    },
    {
        "type": "OSC Differential",
        "s": "TL4",
        "size": "5.0*3.2/6/CMOS/Fast deliver",
        "code": "3P"
    },
    {
        "type": "OSC Differential",
        "s": "TL5",
        "size": "5.0*3.2/6/LVPECL, LVDS/Fast deliver",
        "code": "4P"
    },
    {
        "type": "OSC Differential",
        "s": "TL6",
        "size": "7.0*5.0/6/CMOS/Fast deliver",
        "code": "5P"
    },
    {
        "type": "OSC Differential",
        "s": "TL7",
        "size": "7.0*5.0/6/LVPECL, LVDS/Fast deliver",
        "code": "6P"
    },
    {
        "type": "OSC Differential",
        "s": "TP3",
        "size": "3.2*2.5/6/LVPECL, LVDS/low jitter",
        "code": "1Q"
    },
    {
        "type": "OSC Differential",
        "s": "TP5",
        "size": "5.0*3.2/6/LVPECL, LVDS/low jitter",
        "code": "2Q"
    },
    {
        "type": "OSC Differential",
        "s": "TP7",
        "size": "7.0*5.0/6/LVPECL, LVDS/low jitter",
        "code": "3Q"
    },
    {
        "type": "OSC Differential",
        "s": "TS7",
        "size": "7.0*5.0/6/HCSL",
        "code": "4Q"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC1",
        "size": "2.0*1.6/4/TCXO/VCTCXO",
        "code": "1C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC4",
        "size": "2.0*1.6/6/TCXO/VCTCXO",
        "code": "AC"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC2",
        "size": "2.5*2.0/4/TCXO/VCTCXO",
        "code": "2C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC3",
        "size": "3.2*2.5/4/TCXO/VCTCXO",
        "code": "3C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC5",
        "size": "5.0*3.2/4/TCXO/VCTCXO",
        "code": "5C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC7",
        "size": "7.0*5.0/4/TCXO/VCTCXO",
        "code": "7C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VC8",
        "size": "14.3*8.7/6/TCXO/VCTCXO",
        "code": "8C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VP3",
        "size": "3.2*2.5/4/TCXO/VCTCXO/High Frequency",
        "code": "4C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VW5",
        "size": "5.0*3.2/4/TCXO/VCTCXO/High Precision",
        "code": "9C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VA7",
        "size": "7.0*5.0/4/TCXO/VCTCXO/ High Precision",
        "code": "6C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VS7",
        "size": "7.0*5.0/4/TCXO/VCTCXO/Stratum 3",
        "code": "0C"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VT7",
        "size": "7.0*5.0/4/TCXO/VCTCXO/High Precision and High Temperature",
        "code": "7T"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "VF2",
        "size": "20.4*12.8 Half Size",
        "code": "8T"
    },
    {
        "type": "TCXO/VCTCXO",
        "s": "WT1",
        "size": "18.5*12/50.8*50.9",
        "code": "6T"
    },
    {
        "type": "OCXO",
        "s": "OC9",
        "size": "9.7*7.5*4.1",
        "code": "1H"
    },
    {
        "type": "OCXO",
        "s": "OC8",
        "size": "14.3*9.3*6.5",
        "code": "2H"
    },
    {
        "type": "OCXO",
        "s": "OCH",
        "size": "20.3*12.7*11.0",
        "code": "3H"
    },
    {
        "type": "OCXO",
        "s": "OCW",
        "size": "20.6*20.6",
        "code": "4H"
    },
    {
        "type": "OCXO",
        "s": "OSD",
        "size": "25.4*22.1",
        "code": "5H"
    },
    {
        "type": "OCXO",
        "s": "OLH",
        "size": "25.4*25.4",
        "code": "6H"
    },
    {
        "type": "OCXO",
        "s": "OCN",
        "size": "25.4*25.4/超低相噪",
        "code": "7H"
    },
    {
        "type": "OCXO",
        "s": "OSH",
        "size": "36.3*27.2",
        "code": "8B"
    },
    {
        "type": "OCXO",
        "s": "OCD",
        "size": "36.3*27.2/双恒温",
        "code": "9H"
    },
    {
        "type": "VCXO",
        "s": "PV3",
        "size": "3.2 x 2.5 x 0.9",
        "code": "2V"
    },
    {
        "type": "VCXO",
        "s": "PV5",
        "size": "5.0 x 3.2 x 1.25",
        "code": "4V"
    },
    {
        "type": "VCXO",
        "s": "PV7",
        "size": "7.0 x 5.0 x1.75",
        "code": "6V"
    },
    {
        "type": "VCXO",
        "s": "PL3",
        "size": "3.2 x 2.5 x 0.9",
        "code": "3V"
    },
    {
        "type": "VCXO",
        "s": "PL5",
        "size": "5.0 x 3.2 x 1.25",
        "code": "5V"
    },
    {
        "type": "VCXO",
        "s": "PL7",
        "size": "7.0 x 5.0 x1.75",
        "code": "7V"
    },
    {
        "type": "VCXO",
        "s": "CL5",
        "size": "5.0 x 3.2 x 1.25",
        "code": "5L"
    },
    {
        "type": "VCXO",
        "s": "CL7",
        "size": "7.0 x 5.0 x1.75",
        "code": "7L"
    },
    {
        "type": "VCXO",
        "s": "CV5",
        "size": "5.0 x 3.2 x 1.25",
        "code": "8L"
    },
    {
        "type": "VCXO",
        "s": "CV7",
        "size": "7.0 x 5.0 x1.75",
        "code": "6L"
    },
    {
        "type": "VCXO",
        "s": "CV8",
        "size": "14.2 x 9.3 x 5.4",
        "code": "8V"
    },
    {
        "type": "VCXO",
        "s": "LV1",
        "size": "Full Size/Half Size",
        "code": "1L"
    },
    {
        "type": "VCXO",
        "s": "LV2",
        "size": "13.9 x 9.1 x 3.6",
        "code": "2L"
    },
    {
        "type": "晶体滤波器",
        "s": "WF1",
        "size": "UM-1:7.8x3.2x7.8 UM-5:7.8x3.2x6.0",
        "code": "1F"
    },
    {
        "type": "晶体滤波器",
        "s": "WF2",
        "size": "HC-49U/HC-49T：11 x 5 x 13.5/11.0",
        "code": "2F"
    },
    {
        "type": "晶体滤波器",
        "s": "WF3",
        "size": "11 x 8.5 to 18 x 12",
        "code": "3F"
    },
    {
        "type": "晶体滤波器",
        "s": "WF4",
        "size": "45 x 21 x 15 52 x 47 x 21",
        "code": "4F"
    },
    {
        "type": "晶体滤波器",
        "s": "WF5",
        "size": "30 x 20 to 45 x 21",
        "code": "5F"
    },
    {
        "type": "晶体滤波器",
        "s": "WF6",
        "size": "30 x 20 to 60 x 25",
        "code": "6F"
    },
    {
        "type": "晶体滤波器",
        "s": "WF7",
        "size": "7.0 x 5.0 x 1.3",
        "code": "7F"
    }
]

function frequencyGenerate(number) {
    if (number >= 1000000000) {
        return (number / 1000000000).toFixed(3).replace(/\.?0+$/, '') + 'GHz';
    } else if (number >= 1000000) {
        return (number / 1000000).toFixed(3).replace(/\.?0+$/, '') + 'MHz';
    } else if (number >= 1000) {
        return (number / 1000).toFixed(3).replace(/\.?0+$/, '') + 'KHz';
    } else {
        return number + 'Hz';
    }
}
