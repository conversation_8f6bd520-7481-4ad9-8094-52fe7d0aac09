const obj =
    [
        {
            type: "谐振器",
            en: "",
            param: [
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "9Y", str: "SR9"},
                        {code: "8Y", str: "SR8"},
                        {code: "7Y", str: "SR7"},
                        {code: "5Y", str: "SR5"},
                        {code: "4Y", str: "SR4"},
                        {code: "3Y", str: "SR3"},
                        {code: "2Y", str: "SR2"},
                        {code: "1Y", str: "SR1"},
                        {code: "6W", str: "SR0"},
                        {code: "6Y", str: "SR6"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差/带宽",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "±25KHz"},
                        {code: "B", str: "±50KHz"},
                        {code: "C", str: "±75KHz"},
                        {code: "D", str: "±100KHz"},
                        {code: "E", str: "±150KHz"},
                        {code: "F", str: "±250KHz"},
                        {code: "G", str: "3MHz"},
                        {code: "H", str: "±125KHz"},
                    ],
                },
                {
                    name: "插损",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "1.0"},
                        {code: "B", str: "1.2"},
                        {code: "C", str: "1.4"},
                        {code: "D", str: "1.6"},
                        {code: "E", str: "1.8"},
                        {code: "F", str: "1.5"},
                        {code: "G", str: "2.5"},
                        {code: "H", str: "2.0"},
                        {code: "J", str: "2.2"},
                        {code: "K", str: "1.3"},
                        {code: "L", str: "1.7"},
                        {code: "M", str: "6.0"},
                        {code: "N", str: "8.0"},
                    ],
                },
                {
                    name: "工作温度",
                    count: 1,
                    type: "select",
                    list: [
                        // todo 缺少单位，格式不一
                        {code: "A", str: "[ 0 ~ +50]"},
                        {code: "B", str: "[-10 ~ +60]"},
                        {code: "C", str: "[-20 ~ +70]"},
                        {code: "D", str: "[-40 ~ +85]"},
                        {code: "E", str: "[-30 ~ +85]"},
                        {code: "F", str: "[-40 ~ +95]"},
                        {code: "G", str: "[-40 ~ +105]"},
                        {code: "H", str: "[-40 ~ +125]"},
                        {code: "I", str: "[-55 ~ +125]"},
                        {code: "J", str: "-30+85℃"},
                        {code: "K", str: "-10~+70"},
                        {code: "L", str: "-45~+85"},
                        {code: "M", str: "-30+80℃"},
                        {code: "N", str: "0~60"},
                        {code: "O", str: "-30~+75"},
                        {code: "P", str: "-20~+75"},
                        {code: "Q", str: "-40~+125"},
                        {code: "S", str: "-20~+85"},
                    ],
                },
                {
                    name: "特殊要求",
                    count: 1,
                    type: "select",
                    list: [
                        // todo 英文
                        {code: "R", str: "RR 要求"},
                        {code: "D", str: "DLD要求"},
                        {code: "S", str: "寄生要求"},
                        {code: "C", str: "C0要求"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "A", str: "纸编带"},
                        {code: "B", str: "青蛙脚"},
                        {code: "E", str: "穿铁衣 "},
                        {code: "F", str: "弯脚"},
                        {code: "G", str: "4脚"},
                        {code: "2", str: "2脚"},
                    ],
                },
            ],
        },
        //todo RTC未配置
        {
            type: "陶振",
            param: [
                {
                    name: "型号",
                    count: 2,
                    type: "select",
                    list: [
                        {code: "9I", str: "ZA1"},
                        {code: "8I", str: "ZA2"},
                        {code: "7I", str: "ZA3"},
                        {code: "6I", str: "ZA4"},
                        {code: "5I", str: "ZA5"},
                        {code: "4I", str: "ZB1"},
                        {code: "3I", str: "ZB2"},
                        {code: "2I", str: "ZB3"},
                        {code: "1I", str: "ZB4"},
                        {code: "9R", str: "ZT1"},
                        {code: "2R", str: "ZT2"},
                        {code: "3R", str: "ZT3"},
                        {code: "4R", str: "ZT4"},
                        {code: "5R", str: "ZT5"},
                        {code: "7R", str: "ZT6"},
                        {code: "8R", str: "ZT7"},
                        {code: "1R", str: "ZT8"},
                        {code: "9I", str: "ZA1"},
                        {code: "8I", str: "ZA2"},
                        {code: "7I", str: "ZA3"},
                        {code: "6I", str: "ZA4"},
                        {code: "1S", str: "CD1"},
                        {code: "2S", str: "CD1"},
                        {code: "6S", str: "ZA6"},
                        {code: "9V", str: "ZB5"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "±0.2%"},
                        {code: "B", str: "±0.3%"},
                        {code: "C", str: "±0.5%"},
                        {code: "D", str: "±1%"},
                        {code: "E", str: "±0.25%"},
                        {code: "F", str: "±2KHz"},
                        {code: "G", str: "±2.5KHz"},
                        {code: "H", str: "±3KHz"},
                        {code: "I", str: "±6KHz"},
                        {code: "J", str: "±4KHz"},
                        {code: "K", str: "±0.4%"},
                        {code: "L", str: "±7.5KHz"},
                    ],
                },
                {
                    name: "负载",
                    count: 2,
                    type: "select",
                    list: [
                        {code: "00", str: "无负载"},
                        {code: "10", str: "10pF"},
                        {code: "15", str: "15pF"},
                        {code: "16", str: "16pF"},
                        {code: "22", str: "22pF"},
                        {code: "30", str: "30pF"},
                        {code: "33", str: "33pF"},
                        {code: "39", str: "39pF"},
                        {code: "47", str: "47pF"},
                        {code: "05", str: "5pF"},
                        {code: "A0", str: "100PF"},
                        {code: "A1", str: "120pF/470pF"},
                        {code: "A2", str: "33/39pF"},
                    ],
                },
                {
                    name: "TC偏差量",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "A", str: "±0.2%"},
                        {code: "B", str: "±0.3%"},
                        {code: "C", str: "±0.5%"},
                        {code: "D", str: "±1%"},
                        {code: "E", str: "±0.25%"},
                        {code: "F", str: "±2KHz"},
                        {code: "G", str: "±2.5KHz"},
                        {code: "H", str: "±3KHz"},
                        {code: "I", str: "±6KHz"},
                        {code: "J", str: "±0.4%"},
                        {code: "N", str: "N/A"},
                        {code: "K", str: "±15KHz"},
                        {code: "2", str: "失真度2%"},
                    ],
                },
                {
                    name: "TC偏差量",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "S", str: "-20+85℃"},
                        {code: "V", str: "-20+80℃"},
                        {code: "W", str: "-25+85℃ "},
                        {code: "X", str: "-40+80℃ "},
                        {code: "C", str: "-20+70℃"},
                        {code: "Q", str: "-40+125℃"},
                        //  todo：这行-没有code 临时处理
                        {code: "-", str: "-20+105℃"},
                        {code: "D", str: "-40+85℃"},
                        {code: "Y", str: "-25+80℃"},
                        {code: "Z", str: "-45+125℃"},
                    ],
                },
                {
                    name: "特殊要求",
                    count: 1,
                    type: "select",
                    list: [
                        {code: "7", str: "RR <70ohms"},
                        {code: "5", str: "RR<50 ohms"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "A", str: "ZTA（2只脚）"},
                        {code: "B", str: "ZTT（3只脚）"},
                        {code: "S", str: "SMD"},
                        {code: "K", str: "加20KHz"},
                    ],
                },
            ],
        },
        {
            type: "音叉",
            param: [
                {
                    name: "型号",
                    type: "select",
                    list: [
                        {code: "5X", str: "TS7"},
                        {code: "8W", str: "TS8"},
                        {code: "1W", str: "TS9"},
                        {code: "1X", str: "WX1"},
                        {code: "3X", str: "WX3"},
                        {code: "4X", str: "WX4"},
                        {code: "2T", str: "WA1"},
                        {code: "2X", str: "WX2"},
                        {code: "3T", str: "WT8"},
                        {code: "8A", str: "WA8"},
                        {code: "9A", str: "WA4"},
                        {code: "AW", str: "TS6"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    type: "select",
                    list: [
                        {code: "A", str: "±5PPM"},
                        {code: "B", str: "±10PPM"},
                        {code: "C", str: "±15PPM"},
                        {code: "D", str: "±20PPM"},
                        {code: "E", str: "±25PPM"},
                        {code: "F", str: "±30PPM"},
                        {code: "G", str: "±50PPM"},
                        {code: "H", str: "±100PPM"},
                        {code: "I", str: "±150PPM"},
                        {code: "J", str: "±40PPM"},
                        {code: "K", str: "-20~-30PPM"},
                        {code: "L", str: "-50 至 -90ppm"},
                    ],
                },
                {
                    name: "负载",
                    type: "select",
                    list: [
                        {code: "06", str: "6pF"},
                        {code: "07", str: "7pF"},
                        {code: "12", str: "12pF"},
                        {code: "09", str: "9pF"},
                        {code: "20", str: "20pF"},
                        {code: "L5", str: "12.5"},
                        {code: "10", str: "10pF"},
                        {code: "04", str: "4pF"},
                    ],
                },
                {
                    name: "TC偏差量",
                    type: "select",
                    list: [
                        {code: "A", str: "±5PPM"},
                        {code: "B", str: "±10PPM"},
                        {code: "C", str: "±15PPM"},
                        {code: "D", str: "±20PPM"},
                        {code: "E", str: "±25PPM"},
                        {code: "F", str: "±30PPM"},
                        {code: "G", str: "±50PPM"},
                        {code: "H", str: "±100PPM"},
                        {code: "I", str: "±150PPM"},
                        {code: "Z", str: "Total Frequency tolerance"},
                        {code: "J", str: "±40PPM"},
                        {code: "K", str: "0"},
                    ],
                },
                {
                    name: "TC温度",
                    type: "select",
                    list: [
                        // todo 单位
                        {code: "A", str: "[  0 ~  +50]"},
                        {code: "B", str: "[-10 ~ +60]"},
                        {code: "C", str: "[-20 ~ +70]"},
                        {code: "D", str: "[-40 ~ +85]"},
                        {code: "E", str: "[-30 ~ +85]"},
                        {code: "F", str: "[-40 ~ +95]"},
                        {code: "G", str: "[-40 ~ +105]"},
                        {code: "H", str: "[-40 ~ +125]"},
                        {code: "I", str: "[-55 ~ +125]"},
                        {code: "J", str: "[-30 ~ +85]"},
                        {code: "K", str: "[-10 ~ +70]"},
                        {code: "L", str: "[-45 ~ +85]"},
                        {code: "M", str: "[-30 ~ +80]"},
                        {code: "N", str: "[0 ~ +60]"},
                        {code: "O", str: "[-30~+75]"},
                        {code: "P", str: "[-20~+75]"},
                        {code: "Q", str: "[-40~+125]"},
                        {code: "S", str: "[-20~+85]"},
                        {code: "T", str: "[-20 ~ +60]"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    list: [
                        // todo 中文
                        {code: "7", str: "RR <70ohms"},
                        {code: "5", str: "RR<50 ohms"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "L", str: "脚长6mm"},
                        {code: "T", str: "TKD"},
                        {code: "3", str: "RR<30KΩ max"},
                        {code: "A", str: "脚长2.3mm"},
                    ],
                },
            ],
        },
        {
            type: "crystal",
            param: [
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "1M", str: "TX1"},
                        {code: "2M", str: "TX2"},
                        {code: "3M", str: "TX3"},
                        {code: "5M", str: "TX5"},
                        {code: "6M", str: "TX6"},
                        {code: "7M", str: "TX7"},
                        {code: "8M", str: "TX8"},
                        {code: "4M", str: "TX9"},
                        {code: "5G", str: "TG5"},
                        {code: "6G", str: "TG6"},
                        {code: "7G", str: "TG7"},
                        {code: "8G", str: "TG8"},
                        {code: "9S", str: "WX6"},
                        {code: "6U", str: "WS6"},
                        {code: "3S", str: "WZ6"},
                        {code: "2A", str: "WA2"},
                        {code: "3A", str: "WA3"},
                        {code: "4N", str: "WZ4"},
                        {code: "4S", str: "WZ5"},
                        {code: "5S", str: "WZ3"},
                        {code: "9M", str: "WX7"},
                        {code: "7S", str: "WZ7"},
                        {code: "9L", str: "WS7"},
                        {code: "9U", str: "WX5"},
                        {code: "8U", str: "WZ1"},
                        {code: "1U", str: "WM1"},
                        {code: "2W", str: "W12"},
                        //  todo:这里没有系列号？
                        {code: "L1", str: ""},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5PPM"},
                        {code: "B", str: "±10PPM"},
                        {code: "C", str: "±15PPM"},
                        {code: "D", str: "±20PPM"},
                        {code: "E", str: "±25PPM"},
                        {code: "F", str: "±30PPM"},
                        {code: "G", str: "±50PPM"},
                        {code: "H", str: "±100PPM"},
                        {code: "I", str: "±150PPM"},
                        {code: "J", str: "±35PPM"},
                        {code: "K", str: "±40PPM "},
                        {code: "L", str: "±7PPM"},
                        {code: "M", str: "±17PPM"},
                        {code: "N", str: "±8PPM"},
                    ],
                },
                {
                    name: "负载",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "06", str: "6pF"},
                        {code: "08", str: "8pF"},
                        {code: "12", str: "12pF"},
                        {code: "I3", str: "9.3pF"},
                        {code: "L5", str: "12.5pF"},
                        {code: "20", str: "20pf"},
                        {code: "00", str: "无负载"},
                        {code: "18", str: "18pF"},
                        {code: "09", str: "9PF"},
                        {code: "16", str: "16PF"},
                        {code: "30", str: "30pf"},
                        {code: "33", str: "33ppF"},
                        {code: "15", str: "15PF"},
                        {code: "10", str: "10PF"},
                        {code: "13", str: "13PF"},
                        {code: "I5", str: "9.5pF"},
                        {code: "I8", str: "9.8pF"},
                        {code: "07", str: "7pF"},
                    ],
                },
                {
                    name: "TC偏差量",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5PPM"},
                        {code: "B", str: "±10PPM"},
                        {code: "C", str: "±15PPM"},
                        {code: "D", str: "±20PPM"},
                        {code: "E", str: "±25PPM"},
                        {code: "F", str: "±30PPM"},
                        {code: "G", str: "±50PPM"},
                        {code: "H", str: "±100PPM"},
                        {code: "I", str: "±150PPM"},
                        {code: "J", str: "±35PPM"},
                        {code: "K", str: "±6PPM"},
                        {code: "L", str: "±40PPM"},
                    ],
                },
                {
                    name: "TC温度",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 格式不一，单位
                        {code: "A", str: "[  0 ~  +50]"},
                        {code: "B", str: "[-10 ~ +60]"},
                        {code: "C", str: "[-20 ~ +70]"},
                        {code: "D", str: "[-40 ~ +85]"},
                        {code: "E", str: "[-30 ~ +85]"},
                        {code: "F", str: "[-40 ~ +95]"},
                        {code: "G", str: "[-40 ~ +105]"},
                        {code: "H", str: "[-40 ~ +125]"},
                        {code: "I", str: "[-55 ~ +125]"},
                        {code: "J", str: "[-30 ~ +70]"},
                        {code: "K", str: "[-10 ~ +70]"},
                        {code: "L", str: "[-45 ~ +85]"},
                        {code: "M", str: "[-30 ~ +80]"},
                        {code: "N", str: "[0 ~ +60]"},
                        {code: "O", str: "[-30~+75]"},
                        {code: "P", str: "[-20~+75]"},
                        {code: "Q", str: "[-40~+125]"},
                        {code: "R", str: "0~70"},
                        {code: "S", str: "[-20~+85]"},
                        {code: "T", str: "-20+60℃"},
                        {code: "U", str: "-20+105℃"},
                        {code: "V", str: "-20+80℃"},
                        {code: "W", str: "-25+85℃ "},
                        {code: "X", str: "-40+80℃ "},
                        {code: "Y", str: "-25+80℃"},
                        {code: "Z", str: "-45+125℃"},
                        {code: "1", str: "10°C~50°C"},
                        {code: "2", str: "-10~+85℃"},
                        {code: "3", str: "-10~+75℃"},
                        {code: "4", str: "-40 to +90°C"},
                        {code: "5", str: "[-30 ~ +105]"},
                    ],
                },
                {
                    name: "振动模式",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "1", str: "AT Fundamental"},
                        {code: "3", str: "AT 3RD"},
                        {code: "5", str: "AT 5TH"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "R", str: "RR 要求"},
                        {code: "D", str: "DLD要求"},
                        {code: "S", str: "寄生要求"},
                        {code: "C", str: "C0要求"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "A", str: "纸编带"},
                        {code: "E", str: "剪脚"},
                        {code: "F", str: "穿铁衣 "},
                        {code: "B", str: "弯脚"},
                        {code: "G", str: "4脚"},
                        {code: "H", str: "3脚"},
                        {code: "K", str: "ESR 20 ohm max"},
                        {code: "2", str: "ESR 25 ohm max"},
                        {code: "5", str: "ESR 50 ohm max"},
                        {code: "3", str: "ESR 30 ohm max"},
                        {code: "6", str: "ESR 60 ohm max"},
                        {code: "R", str: "ESR 100 ohm max"},
                        {code: "4", str: "ESR 40 ohm max"},
                        {code: "Z", str: "200 Ohm"},
                        {code: "8", str: "ESR 80 ohm max"},
                        {code: "7", str: "ESR 70 ohm max"},
                        {code: "L", str: "ESR 120 ohm max"},
                        {code: "9", str: "ESR 45 ohm max"},
                        {code: "I", str: "ESR 35 ohm max"},
                    ],
                },
            ],
        },
        {
            type: "钟振",
            param: [
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "1K", str: "TC1"},
                        {code: "2K", str: "TC2"},
                        {code: "3K", str: "TC3"},
                        {code: "5K", str: "TC5"},
                        {code: "7K", str: "TC7"},
                        {code: "4K", str: "TK2"},
                        {code: "6K", str: "TK3"},
                        {code: "8K", str: "TK4"},
                        {code: "9K", str: "TK4"},
                        {code: "1D", str: "TU2"},
                        {code: "2D", str: "TU3"},
                        {code: "3D", str: "TW2"},
                        {code: "4D", str: "TW3"},
                        {code: "5D", str: "TN2"},
                        {code: "6D", str: "TN3"},
                        {code: "7D", str: "TN7"},
                        {code: "8F", str: "LO1"},
                        {code: "8H", str: "LO2"},
                        {code: "1B", str: "TB1"},
                        {code: "2B", str: "TB2"},
                        {code: "3B", str: "TB3"},
                        {code: "5B", str: "TB4"},
                        {code: "7B", str: "TB5"},
                        {code: "1P", str: "TL2"},
                        {code: "2P", str: "TL3"},
                        {code: "3P", str: "TL4"},
                        {code: "4P", str: "TL5"},
                        {code: "5P", str: "TL6"},
                        {code: "6P", str: "TL7"},
                        {code: "1Q", str: "TP3"},
                        {code: "2Q", str: "TP5"},
                        {code: "3Q", str: "TP7"},
                        {code: "4Q", str: "TS7"},
                        {code: "1T", str: "VC9"},
                        {code: "AC", str: "VC4"},
                        {code: "1C", str: "VC1"},
                        {code: "2C", str: "VC2"},
                        {code: "3C", str: "VC3"},
                        {code: "5C", str: "VC5"},
                        {code: "7C", str: "VC7"},
                        {code: "8C", str: "VC8"},
                        {code: "4C", str: "VP3"},
                        {code: "9C", str: "VW5"},
                        {code: "6C", str: "VA7"},
                        {code: "9T", str: "VS7"},
                        {code: "7T", str: "VT7"},
                        {code: "8T", str: "VF2"},
                        {code: "6T", str: "WT1"},
                        {code: "1H", str: "OC9"},
                        {code: "2H", str: "OC8"},
                        {code: "3H", str: "OCH"},
                        {code: "4H", str: "OCW"},
                        {code: "5H", str: "OSD"},
                        {code: "6H", str: "OLH"},
                        {code: "7H", str: "OCN"},
                        {code: "8B", str: "OSH"},
                        {code: "9H", str: "OCD"},
                        {code: "2V", str: "PV3"},
                        {code: "4V", str: "PV5"},
                        {code: "6V", str: "PV7"},
                        {code: "3V", str: "PL3"},
                        {code: "5V", str: "PL5"},
                        {code: "7V", str: "PL7"},
                        {code: "5L", str: "CL5"},
                        {code: "7L", str: "CL7"},
                        {code: "8L", str: "CV5"},
                        {code: "6L", str: "CV7"},
                        {code: "8V", str: "CV8"},
                        {code: "1L", str: "LV1"},
                        {code: "2L", str: "LV2"},
                        {code: "1G", str: "TC8"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },

                {
                    name: "总频差",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5PPM"},
                        {code: "B", str: "±10PPM"},
                        {code: "C", str: "±15PPM"},
                        {code: "D", str: "±20PPM"},
                        {code: "E", str: "±25PPM"},
                        {code: "F", str: "±30PPM"},
                        {code: "G", str: "±50PPM"},
                        {code: "H", str: "±100PPM"},
                        {code: "I", str: "±0.5PPM"},
                        {code: "J", str: "±1.0PPM"},
                        {code: "K", str: "±1.5PPM"},
                        {code: "T", str: "±2.0PPM"},
                        {code: "P", str: "±2.5PPM"},
                        {code: "Q", str: "±0.05PPM"},
                        {code: "R", str: "±0.1PPM"},
                        {code: "S", str: "±0.2PPM"},
                        // todo 正负？0？
                        {code: "T", str: "0"},
                        {code: "U", str: "95.8464PPM"},
                        {code: "V", str: "±4.6PPM"},
                        {code: "W", str: "±5.0PPM"},
                        {code: "X", str: "±20PPb"},
                    ],
                },
                {
                    name: "供电电压",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "18", str: "1.8V"},
                        {code: "25", str: "2.5V"},
                        {code: "28", str: "2.8V "},
                        {code: "33", str: "3.3V"},
                        {code: "50", str: "5.0V"},
                        {code: "12", str: "1.2V"},
                        {code: "90", str: "9.0V"},
                        {code: "09", str: "0.9V"},
                        {code: "30", str: "3.0V"},
                        {code: "99", str: "1.8~3.3V"},
                        {code: "88", str: "-0.3~4.6V"},
                        {code: "77", str: "1.7~3.3V"},
                        {code: "66", str: "2.8~3.3V"},
                        {code: "55", str: "1.7V~3.465V"},
                        {code: "A", str: "12V"},
                        {code: "44", str: "1.6~3.63V"},
                        {code: "22", str: "2.2v"},
                    ],
                },
                {
                    name: "TC偏差量",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "A", str: "±5PPM"},
                        {code: "B", str: "±10PPM"},
                        {code: "C", str: "±15PPM"},
                        {code: "D", str: "±20PPM"},
                        {code: "E", str: "±25PPM"},
                        {code: "F", str: "±30PPM"},
                        {code: "G", str: "±50PPM"},
                        {code: "H", str: "±100PPM"},
                        {code: "I", str: "±150PPM"},
                        {code: "J", str: "±40PPM"},
                        //  todo 中文？
                        {code: "Z", str: "总频差"},
                        {code: "K", str: "±0.5PPM"},
                        {code: "L", str: "±3ppb"},
                        {code: "M", str: "±0.28"},
                        {code: "N", str: "±2.5ppm"},
                        //  todo O没有对应ppm
                        {code: "O", str: ""},
                        {code: "P", str: "±1.0ppm"},
                        {code: "Q", str: "±2ppm"},
                        {code: "R", str: "±3ppm"},
                        {code: "S ", str: "±50ppb"},
                        {code: "T", str: "±0.1ppm"},
                        {code: "U", str: "±0.14ppm"},
                        {code: "V", str: "±0.2ppm"},
                        {code: "W", str: "±0.28ppm"},
                        {code: "X", str: "±0.37ppm"},
                        {code: "Y", str: "±0.5ppm"},
                        {code: "Z", str: "±0.5ppb"},
                        {code: "02", str: "±0.2ppb"},
                        {code: "05", str: "±0.5ppb"},
                        {code: "1", str: "±1ppb"},
                        {code: "5", str: "±5ppb"},
                        {code: "10", str: "±10ppb"},
                        {code: "20", str: "±20ppb"},
                        {code: "30", str: "±30ppb"},
                    ],
                },
                {
                    name: "TC温度",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 格式不一，单位
                        {code: "A", str: "[  0 ~  +50]"},
                        {code: "B", str: "[-10 ~ +60]"},
                        {code: "C", str: "[-20 ~ +70]"},
                        {code: "D", str: "[-40 ~ +85]"},
                        {code: "E", str: "[-30 ~ +85]"},
                        {code: "F", str: "[-40 ~ +95]"},
                        {code: "G", str: "[-40 ~ +105]"},
                        {code: "H", str: "[-40 ~ +125]"},
                        {code: "I", str: "[-55 ~ +125]"},
                        {code: "J", str: "[-30 ~ +85]"},
                        {code: "K", str: "[-10 ~ +70]"},
                        {code: "L", str: "[-45 ~ +85]"},
                        {code: "M", str: "[-30 ~ +80]"},
                        {code: "N", str: "[0 ~ +60]"},
                        {code: "O", str: "[-30~+75]"},
                        {code: "P", str: "[-20~+75]"},
                        {code: "Q", str: "[-40~+125]"},
                        {code: "R", str: "0~70"},
                        {code: "S", str: "[-20~+85]"},
                        {code: "T", str: "-20+60°C"},
                        {code: "U", str: "-20+105℃"},
                        {code: "V", str: "-20+80℃"},
                        {code: "W", str: "-25+85℃"},
                        {code: "X", str: "-40+80℃"},
                        {code: "Y", str: "-25+80℃"},
                        {code: "Z", str: "-45+125℃"},
                        {code: "7", str: " -30+70℃"},
                        {code: "5", str: "-25-70℃"},
                        {code: "4", str: "-40 +70℃"},
                        {code: "8", str: "-50 +85℃"},
                    ],
                },
                {
                    name: "输出模式",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "C", str: " CMOS/HCMOS（方波）"},
                        {code: "P", str: " PECL(6脚）"},
                        {code: "L", str: " LVDS"},
                        {code: "T", str: " TTL"},
                        {code: "H", str: "HCSL"},
                        {code: "M", str: "CML"},
                        {code: "V", str: " LVPECL"},
                        {code: "A", str: "Clipped Sine Wave（削峰正弦波TCXO）"},
                        {code: "B", str: "Sine Wave（正弦波OC）"},
                        {code: "E", str: "Pulse"},
                        {code: "D", str: "LVTTL"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    count: 1,
                    // todo 中文
                    list: [
                        {code: "L", str: " Low Jitter要求"},
                        {code: "P", str: "Phase Noise 要求"},
                        {code: "O", str: "其它要求"},
                        {code: "N", str: "无特别要求"},
                        {code: "Q", str: "电流：10mA max"},
                    ],
                },
            ],
        },
        {
            type: "天线",
            param: [
                {
                    name: "型号",
                    type: "select",
                    list: [{code: "DA", str: "DA"}],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },
                {
                    name: "频率容差",
                    type: "select",
                    list: [
                        {code: "A", str: "±5"},
                        {code: "B", str: "±10"},
                        {code: "C", str: "±1.0"},
                        {code: "D", str: "±1.5"},
                        {code: "E", str: "±2.0"},
                        {code: "F", str: "±2.5"},
                        {code: "G", str: "±3.0"},
                        {code: "H", str: "±4.0"},
                        {code: "J", str: "±4.5"},
                        {code: "K", str: "±1.023"},
                    ],
                },
                {
                    name: "极化方式",
                    type: "select",
                    list: [
                        {code: "R", str: "RHCP"},
                        {code: "L", str: "LHCP"},
                        {code: "P", str: "Linear Polarization"},
                    ],
                },
                {
                    name: "长X宽",
                    type: "select",
                    list: [
                        {code: "3535", str: "3535"},
                        {code: "2525", str: "2525"},
                        {code: "2020", str: "2020"},
                        {code: "1818", str: "1818"},
                        {code: "2006", str: "2006"},
                        {code: "1606", str: "1606"},
                    ],
                },
                {
                    name: "应用",
                    type: "select",
                    list: [
                        {code: "G", str: "GPS"},
                        {code: "B", str: "BD"},
                        {code: "S", str: "Glonass"},
                        {code: "A", str: "GPS+BD"},
                        {code: "C", str: "GPS+Glonass"},
                        {code: "D", str: "DSRC"},
                        {code: "E", str: "ETC"},
                        {code: "R", str: "RFID"},
                    ],
                },
            ],
        },
        {
            type: "Filter滤波器",
            param: [
                {
                    name: "型号",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "1Z", str: "CF1"},
                        {code: "2Z", str: "CF1"},
                        {code: "3Z", str: "CF2"},
                        {code: "4Z", str: "CF2"},
                        {code: "5Z", str: "CF3"},
                        {code: "6Z", str: "CF3"},
                        {code: "7Z", str: "CF4"},
                        {code: "8Z", str: "CF5"},
                        {code: "9Z", str: "CF5"},
                        {code: "AZ", str: "CF6"},
                        {code: "9E", str: "SF9"},
                        {code: "8E", str: "SF8"},
                        {code: "7E", str: "SF7"},
                        {code: "5E", str: "SF5"},
                        {code: "4E", str: "SF4"},
                        {code: "3E", str: "SF3"},
                        {code: "2E", str: "SF2"},
                        {code: "1E", str: "SF1"},
                        {code: "6E", str: "SF0"},
                        {code: "1J", str: "SW1"},
                        {code: "2J", str: "SW2"},
                        {code: "3J", str: "SW3"},
                        {code: "4J", str: "SW4"},
                        {code: "5J", str: "SW5"},
                        {code: "6J", str: "SW6"},
                        {code: "7J", str: "SW7"},
                        {code: "8J", str: "SW8"},
                        {code: "9J", str: "SW9"},
                        {code: "1F", str: "WF1"},
                        {code: "2F", str: "WF2"},
                        {code: "3F", str: "WF3"},
                        {code: "4F", str: "WF4"},
                        {code: "5F", str: "WF5"},
                        {code: "6F", str: "WF6"},
                        {code: "7F", str: "WF7"},
                        {code: "1A", str: "SWA"},
                    ],
                },
                {
                    name: "频率",
                    type: "number",
                    generate: frequencyGenerate,
                },
                {
                    name: "类别",
                    type: "select",
                    count: 1,
                    list: [
                        {code: "C", str: "ceramic fiter"},
                        {code: "S", str: " Saw Filter"},
                        {code: "Q", str: " Crystal Filter"},
                    ],
                },
                {
                    name: "带宽",
                    type: "select",
                    list: [
                        {code: "51", str: "51MHz"},
                        {code: "05", str: "5MHz"},
                        //  todo 中文
                        {code: "TW", str: "多种带宽"},
                        {code: "06", str: "6KHz "},
                        {code: "15", str: "7.5KHz "},
                        {code: "02", str: "2MHz"},
                        {code: "0A", str: "1.75MHz"},
                        {code: "00", str: "无带宽"},
                        {code: "0B", str: "1.2MHz"},
                        {code: "08", str: " 8MHz"},
                        {code: "0C", str: "31.24"},
                        {code: "12", str: "12MHz"},
                        {code: "01", str: " 1MHz"},
                        {code: "20", str: "20MHz"},
                        {code: "A3", str: "1.3MHz"},
                        {code: "45", str: "45MHz"},
                        {code: "0D", str: "3.75KHz"},
                        {code: "17", str: "17MHZ"},
                        {code: "0E", str: "18.6KHz"},
                        {code: "02", str: "2KHz"},
                        {code: "40", str: "40MHz"},
                        {code: "0F", str: "1.71MHz"},
                        {code: "A5", str: "1.5KHz"},
                        {code: "A1", str: "11KHz"},
                        {code: "B5", str: "2.5KHz"},
                        {code: "04", str: "4KHz"},
                        {code: "N5", str: "0.5KHz"},
                        {code: "A2", str: "1.2KHz"},
                        {code: "0G", str: "0.15KHz"},
                        {code: "0H", str: "0.12KHz"},
                        {code: "0I", str: "2.2KHz"},
                        {code: "0J", str: "0.08KHz"},
                        {code: "0K", str: "0.95KHz"},
                        {code: "0L", str: "0.4KHz"},
                        {code: "0M", str: "1.1KHz"},
                        {code: "0N", str: "0.55KHz"},
                        {code: "0O", str: "15KHz"},
                        {code: "0P", str: "34KHz"},
                        {code: "0Q", str: "2.778KHz"},
                        {code: "0R", str: "19KHz"},
                        {code: "0S", str: "7.0KHz"},
                        {code: "0T", str: "60KHz"},
                        {code: "0U", str: "5.0KHz"},
                        {code: "0V", str: "12KHz"},
                        {code: "0W", str: "17KHz"},
                        {code: "0X", str: "5.5KHz"},
                        {code: "0Y", str: "1.7KHz"},
                        {code: "0Z", str: "3.5KHz"},
                        {code: "1A", str: "3.8KHz"},
                        {code: "1B", str: "9KHz"},
                        {code: "1C", str: "26KHz"},
                        {code: "1D", str: "40kHz"},
                        {code: "1E", str: "80kHz"},
                        {code: "1F", str: "3.94MHz"},
                        {code: "31", str: "31MHz"},
                        {code: "1G", str: "46.41MHz"},
                    ],
                },
                {
                    name: "插损",
                    type: "select",
                    count: 2,
                    list: [
                        {code: "01", str: " 1.0 dB"},
                        {code: "22", str: "2.2dB"},
                        {code: "05", str: "5.0dB"},
                        {code: "02", str: "2.0dB"},
                        {code: "03", str: "3.0dB"},
                        {code: "04", str: "4.0dB"},
                        {code: "0A", str: " 1.4dB"},
                        {code: "0B", str: "2.5dB"},
                        {code: "1B", str: "4.1dB"},
                        {code: "35", str: "3.5dB"},
                        {code: "18", str: "1.8dB"},
                        {code: "0B", str: "1.3dB"},
                        {code: "15", str: "1.5dB"},
                        {code: "06", str: "6.0dB"},
                        {code: "21", str: "21dB"},
                        {code: "20", str: "20dB"},
                        {code: "16", str: "1.6dB"},
                        {code: "45", str: " 4.5dB"},
                        {code: "28", str: " 2.8dB"},
                    ],
                },
                {
                    name: "特殊要求",
                    type: "select",
                    count: 1,
                    list: [
                        // todo 中文
                        {code: "N", str: "无"},
                        {code: "D", str: "双模"},
                        {code: "T", str: "三模"},
                        {code: "3", str: "3RD"},
                    ],
                },
            ],
        },
    ];

function frequencyGenerate(number) {
    if (number >= 1000000000) {
        return (number / 1000000000).toFixed(3).replace(/\.?0+$/, '') + 'GHz';
    } else if (number >= 1000000) {
        return (number / 1000000).toFixed(3).replace(/\.?0+$/, '') + 'MHz';
    } else if (number >= 1000) {
        return (number / 1000).toFixed(3).replace(/\.?0+$/, '') + 'KHz';
    } else {
        return number + 'Hz';
    }
}
