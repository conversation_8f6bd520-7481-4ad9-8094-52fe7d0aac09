<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>料号生成器</title>
    <script src="skuOption.js"></script>
    <!-- 引入Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 引入组件库 -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #6c757d;
        }
        .header h1 {
            color: #495057;
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .form-section {
            margin-bottom: 25px;
        }
        .form-section h3 {
            color: #303133;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
        }
        .param-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .param-label {
            width: 120px;
            text-align: right;
            margin-right: 15px;
            color: #606266;
            font-weight: 500;
            flex-shrink: 0;
        }
        .param-control {
            flex: 1;
            min-width: 200px;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #6c757d;
        }
        .result-section h3 {
            color: #495057;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .sku-result {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #DCDFE6;
            word-break: break-all;
            line-height: 1.6;
            font-family: 'Courier New', monospace;
        }
        .specs-result {
            font-size: 14px;
            font-weight: normal;
            color: #606266;
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #DCDFE6;
            word-break: break-all;
            line-height: 1.8;
        }
        .frequency-input {
            display: flex;
            align-items: center;
        }
        .frequency-input .el-input {
            margin-right: 10px;
        }
        .frequency-display {
            color: #909399;
            font-size: 14px;
        }
        .decode-tag {
            margin-top: 8px;
        }
        .decode-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        .decode-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 2px solid #6c757d;
            padding-bottom: 8px;
        }
        .generate-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        .generate-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 2px solid #6c757d;
            padding-bottom: 8px;
        }
        .el-divider__text {
            background-color: #f5f5f5;
            color: #6c757d;
            font-weight: 500;
        }
        .param-table {
            margin-top: 15px;
        }
        .param-table .el-table th {
            background-color: #f5f7fa !important;
        }
        .param-table .frequency-input {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .param-table .frequency-display {
            color: #909399;
            font-size: 12px;
            white-space: nowrap;
        }
        .horizontal-table {
            overflow-x: auto;
        }
        .horizontal-table .el-table__header th {
            font-weight: 600;
            font-size: 13px;
        }
        .horizontal-table .el-table__body td {
            padding: 15px 8px;
            vertical-align: top;
        }
        .horizontal-table .el-select {
            width: 100%;
        }
        .horizontal-table .frequency-display {
            font-size: 11px;
            color: #67c23a;
            margin-top: 4px;
        }
        @media (max-width: 768px) {
            .param-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .param-label {
                width: 100%;
                text-align: left;
                margin-bottom: 8px;
                margin-right: 0;
            }
            .param-control {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>料号生成器</h1>
                <p style="color: #909399; margin: 10px 0 0 0;">选择产品类型和参数，自动生成对应的料号</p>
            </div>

            <!-- 料号反编译区域 -->
            <div class="form-section decode-section">
                <h3><i class="el-icon-search"></i> 料号反编译</h3>
                <div class="param-row">
                    <div class="param-label">输入料号：</div>
                    <div class="param-control">
                        <el-input
                            v-model="inputSku"
                            placeholder="请输入要反编译的料号，如：W9Y000032768AAAR"
                            @input="decodeSku"
                            clearable
                            style="width: 100%">
                            <el-button slot="append" @click="decodeSku" icon="el-icon-search">解析</el-button>
                        </el-input>
                        <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                            <i class="el-icon-info"></i>
                            支持的料号示例：W9Y000032768AAAR
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <el-divider>
                <i class="el-icon-s-tools"></i> 或者手动配置生成
            </el-divider>

            <!-- 料号生成区域 -->
            <div class="form-section generate-section">
                <h3><i class="el-icon-plus"></i> 料号生成</h3>
                <div class="param-row">
                    <div class="param-label">产品类型：</div>
                    <div class="param-control">
                        <el-select v-model="selectedType" placeholder="请选择产品类型" @change="onTypeChange" style="width: 100%">
                            <el-option
                                v-for="(item, index) in productTypes"
                                :key="index"
                                :label="item.type"
                                :value="index">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>

            <div v-if="currentProduct" class="form-section">
                <h3><i class="el-icon-setting"></i> 参数配置</h3>

                <!-- 横向表格容器 -->
                <div style="overflow-x: auto; margin-top: 15px;">
                    <!-- 横向表格 - 参数名称作为表头 -->
                    <el-table
                        :data="[{}]"
                        border
                        class="param-table horizontal-table"
                        style="min-width: 100%"
                        :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
                        :cell-style="{ textAlign: 'center' }">

                    <!-- 为每个参数创建一列 -->
                    <el-table-column
                        v-for="(param, paramIndex) in currentProduct.param"
                        :key="paramIndex"
                        :label="param.name"
                        min-width="150"
                        align="center">
                        <template slot-scope="scope">
                            <!-- 下拉选择类型 -->
                            <el-select
                                v-if="param.type === 'select'"
                                v-model="paramValues[paramIndex]"
                                :placeholder="'请选择'"
                                @change="generateSku"
                                style="width: 100%"
                                size="small">
                                <el-option
                                    v-for="option in param.list"
                                    :key="option.code"
                                    :label="option.str + ' (' + option.code + ')'"
                                    :value="option.code">
                                </el-option>
                            </el-select>

                            <!-- 数字输入类型 -->
                            <div v-else-if="param.type === 'number'">
                                <el-input
                                    v-model.number="paramValues[paramIndex]"
                                    type="number"
                                    :placeholder="'请输入'"
                                    @input="generateSku"
                                    size="small"
                                    style="margin-bottom: 5px;">
                                </el-input>
                                <div v-if="paramValues[paramIndex]" class="frequency-display">
                                    {{ formatFrequency(paramValues[paramIndex]) }}
                                </div>
                            </div>


                        </template>
                    </el-table-column>

                    </el-table>
                </div>
            </div>

            <!-- 空状态提示 -->
            <div v-if="!currentProduct" class="form-section" style="text-align: center; padding: 40px;">
                <i class="el-icon-info" style="font-size: 48px; color: #c0c4cc; margin-bottom: 16px;"></i>
                <p style="color: #909399; margin: 0; font-size: 16px;">请先选择产品类型或输入料号进行解析</p>
            </div>

            <div v-if="generatedSku || (inputSku && decodeResult)" class="result-section">
                <h3><i class="el-icon-document"></i> 结果</h3>

                <!-- 料号显示 -->
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #495057; margin-bottom: 10px;">料号：</h4>
                    <div class="sku-result">{{ generatedSku || inputSku }}</div>
                </div>

                <!-- 规格参数显示 -->
                <div v-if="generatedSpecs">
                    <h4 style="color: #495057; margin-bottom: 10px;">规格参数：</h4>
                    <div class="specs-result">{{ generatedSpecs }}</div>
                </div>

                <!-- 操作按钮 -->
                <div style="margin-top: 15px;">
                    <el-button type="primary" @click="copyResult" icon="el-icon-document-copy">复制完整信息</el-button>
                    <el-button @click="copySku" icon="el-icon-document-copy">仅复制料号</el-button>
                    <el-button @click="resetAll" icon="el-icon-refresh">清空重置</el-button>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    productTypes: obj, // 使用从 skuOption.js 导入的 obj
                    selectedType: null,
                    currentProduct: null,
                    paramValues: {},
                    generatedSku: '',
                    generatedSpecs: '',
                    inputSku: '', // 输入的料号
                    decodedValues: {}, // 解析出的参数值
                    decodeResult: null // 解析结果
                }
            },
            methods: {
                onTypeChange() {
                    if (this.selectedType !== null) {
                        this.currentProduct = this.productTypes[this.selectedType];
                        // 保留已解析的值，只清空生成相关的数据
                        if (!this.decodeResult) {
                            this.paramValues = {};
                        }
                        this.generatedSku = '';
                        this.generatedSpecs = '';

                        // 初始化参数值（如果没有解析结果）
                        if (!this.decodeResult) {
                            this.currentProduct.param.forEach((param, index) => {
                                if (param.type === 'select' && param.list && param.list.length > 0) {
                                    // select类型默认选择第一项
                                    this.$set(this.paramValues, index, param.list[0].code);
                                } else {
                                    // 其他类型默认为空
                                    this.$set(this.paramValues, index, '');
                                }
                            });

                            // 设置默认值后自动生成料号
                            this.$nextTick(() => {
                                this.generateSku();
                            });
                        }
                    }
                },

                decodeSku() {
                    if (!this.inputSku || this.inputSku.trim() === '') {
                        this.resetDecodeResult();
                        return;
                    }

                    const sku = this.inputSku.trim().toUpperCase();
                    let bestMatch = null;
                    let bestMatchScore = 0;

                    // 遍历所有产品类型，尝试匹配
                    this.productTypes.forEach((productType, typeIndex) => {
                        const result = this.tryDecodeSku(sku, productType, typeIndex);
                        if (result && result.score > bestMatchScore) {
                            bestMatch = result;
                            bestMatchScore = result.score;
                        }
                    });

                    if (bestMatch) {
                        this.applyDecodeResult(bestMatch);
                        this.$message.success('料号解析成功！');
                    } else {
                        this.resetDecodeResult();
                        this.$message.warning('无法识别该料号格式，请检查输入是否正确');
                    }
                },

                tryDecodeSku(sku, productType, typeIndex) {
                    let position = 0;
                    let matchedParams = {};
                    let decodedValues = {};
                    let score = 0;

                    for (let paramIndex = 0; paramIndex < productType.param.length; paramIndex++) {
                        const param = productType.param[paramIndex];

                        if (param.type === 'select') {
                            const count = param.count || 1;
                            if (position + count > sku.length) break;

                            const code = sku.substr(position, count);
                            const option = param.list.find(opt => opt.code === code);

                            if (option) {
                                matchedParams[paramIndex] = code;
                                decodedValues[paramIndex] = option.str + ' (' + code + ')';
                                score += 10; // 匹配到选项得10分
                                position += count;
                            } else {
                                // 尝试部分匹配
                                let partialMatch = false;
                                for (let len = Math.min(count, sku.length - position); len > 0; len--) {
                                    const partialCode = sku.substr(position, len);
                                    const partialOption = param.list.find(opt => opt.code === partialCode);
                                    if (partialOption) {
                                        matchedParams[paramIndex] = partialCode;
                                        decodedValues[paramIndex] = partialOption.str + ' (' + partialCode + ')';
                                        score += 5; // 部分匹配得5分
                                        position += len;
                                        partialMatch = true;
                                        break;
                                    }
                                }
                                if (!partialMatch) {
                                    // 跳过这个参数
                                    position += Math.min(count, sku.length - position);
                                }
                            }
                        } else if (param.type === 'number') {
                            // 对于数字类型，尝试提取数字
                            let numberStr = '';
                            let numberEnd = position;

                            // 查找连续的数字和小数点
                            while (numberEnd < sku.length && /[0-9.]/.test(sku[numberEnd])) {
                                numberStr += sku[numberEnd];
                                numberEnd++;
                            }

                            if (numberStr) {
                                const number = parseFloat(numberStr);
                                if (!isNaN(number)) {
                                    matchedParams[paramIndex] = number;
                                    if (param.generate) {
                                        decodedValues[paramIndex] = param.generate(number);
                                    } else {
                                        decodedValues[paramIndex] = numberStr;
                                    }
                                    score += 8; // 数字匹配得8分
                                    position = numberEnd;
                                }
                            }
                        }
                    }

                    // 如果匹配了足够多的参数，认为是有效匹配
                    if (score > 0 && Object.keys(matchedParams).length > 0) {
                        return {
                            typeIndex: typeIndex,
                            productType: productType,
                            matchedParams: matchedParams,
                            decodedValues: decodedValues,
                            score: score
                        };
                    }

                    return null;
                },

                applyDecodeResult(result) {
                    this.selectedType = result.typeIndex;
                    this.currentProduct = result.productType;
                    this.decodedValues = result.decodedValues;
                    this.decodeResult = result;

                    // 同步解析结果到手动配置区域
                    this.paramValues = {};
                    this.currentProduct.param.forEach((param, index) => {
                        if (result.matchedParams[index] !== undefined) {
                            // 使用解析出的值
                            this.$set(this.paramValues, index, result.matchedParams[index]);
                        } else {
                            // 没有解析出值时，如果是select类型则使用第一项作为默认值
                            if (param.type === 'select' && param.list && param.list.length > 0) {
                                this.$set(this.paramValues, index, param.list[0].code);
                            } else {
                                this.$set(this.paramValues, index, '');
                            }
                        }
                    });

                    // 生成对应的规格参数
                    this.generateSpecsFromDecoded();
                },

                generateSpecsFromDecoded() {
                    if (!this.decodeResult) return;

                    let specs = [];
                    Object.keys(this.decodedValues).forEach(key => {
                        if (this.decodedValues[key]) {
                            specs.push(this.decodedValues[key].split(' (')[0]); // 去掉代码部分
                        }
                    });

                    this.generatedSku = this.inputSku.trim().toUpperCase();
                    this.generatedSpecs = this.formatSpecs(specs);
                },

                resetDecodeResult() {
                    this.selectedType = null;
                    this.currentProduct = null;
                    this.paramValues = {};
                    this.decodedValues = {};
                    this.decodeResult = null;
                    this.generatedSku = '';
                    this.generatedSpecs = '';
                },

                generateSku() {
                    if (!this.currentProduct) return;

                    let sku = '';
                    let specs = [];
                    let hasAllRequiredParams = true;

                    this.currentProduct.param.forEach((param, index) => {
                        const value = this.paramValues[index];

                        if (param.type === 'select') {
                            if (value) {
                                // 根据 count 属性决定添加几位
                                const count = param.count || 1;
                                sku += value;

                                // 查找对应的显示文本
                                const option = param.list.find(opt => opt.code === value);
                                if (option) {
                                    specs.push(option.str);
                                }
                            } else {
                                hasAllRequiredParams = false;
                            }
                        } else if (param.type === 'number') {
                            if (value && param.generate) {
                                // 使用频率生成函数
                                const freqStr = param.generate(value);
                                // 提取数字部分作为料号的一部分
                                let numStr = value.toString();
                                // 根据param.length的最小要求，在签名补0
                                numStr = numStr.padStart(param.length, '0');
                                sku += numStr.replace('.', '');
                                specs.push(freqStr);
                            } else if (value) {
                                sku += value.toString().replace('.', '');
                                specs.push(value.toString());
                            } else {
                                hasAllRequiredParams = false;
                            }
                        }
                    });

                    this.generatedSku = hasAllRequiredParams ? sku : '';
                    this.generatedSpecs = hasAllRequiredParams ? this.formatSpecs(specs) : '';
                },

                formatSpecs(specs) {
                    if (!specs || specs.length === 0) return '';

                    // 获取产品类型
                    const productType = this.currentProduct.type;

                    // 构建规格字符串，格式类似：WTL2Y36723PZ，SR9/谐振器，433.92MHz，±75KHz，1.8dB，[-40 ~ +85]，无特别要求
                    let formattedSpecs = this.generatedSku || this.inputSku;

                    // 添加产品类型和规格参数
                    if (specs.length > 0) {
                        // 第一个通常是型号信息，查找对应的尺寸和类型
                        const modelInfo = specs[1] || '';
                        const map = seriesTypeAndSizeMap.find(item => item.s === modelInfo);
                        debugger
                        if (map) {
                            // 如果找到映射，使用映射中的尺寸和类型信息
                            formattedSpecs += '，' + map.size + '/' + map.type;
                        } else {
                            // 如果没找到映射，提示未匹配
                            formattedSpecs += '，未匹配到尺寸和类型';
                        }

                        // 添加其他规格参数
                        for (let i = 2; i < specs.length; i++) {
                            if (specs[i]) {
                                formattedSpecs += '，' + specs[i];
                            }
                        }
                    } else {
                        formattedSpecs += '，' + productType;
                    }

                    return formattedSpecs;
                },

                formatFrequency(number) {
                    if (!number) return '';
                    return frequencyGenerate(number);
                },

                copyResult() {
                    if (this.generatedSku && this.generatedSpecs) {
                        const fullResult = this.generatedSpecs;
                        this.copyToClipboard(fullResult, '完整信息已复制到剪贴板');
                    }
                },

                copySku() {
                    if (this.generatedSku) {
                        this.copyToClipboard(this.generatedSku, '料号已复制到剪贴板');
                    }
                },

                copyToClipboard(text, successMessage) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.$message.success(successMessage);
                    }).catch(() => {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        this.$message.success(successMessage);
                    });
                },

                resetAll() {
                    this.selectedType = null;
                    this.currentProduct = null;
                    this.paramValues = {};
                    this.generatedSku = '';
                    this.generatedSpecs = '';
                    this.inputSku = '';
                    this.decodedValues = {};
                    this.decodeResult = null;
                },

                resetForm() {
                    this.selectedType = null;
                    this.currentProduct = null;
                    this.paramValues = {};
                    this.generatedSku = '';
                    this.generatedSpecs = '';
                    this.inputSku = '';
                    this.decodedValues = {};
                    this.decodeResult = null;
                }
            }
        });
    </script>
</body>
</html>